import { useData } from '../../context/DataContext'
import { forceResetDatabase } from '../../utils/initDatabase'
import StatsCard from '../common/StatsCard'
import AnimatedCard from '../common/AnimatedCard'

const DashboardHome = () => {
  const { kitchensData, cabinetsData, whyChooseUsData, refreshData } = useData()

  const handleResetDatabase = async () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات الحالية.')) {
      try {
        await forceResetDatabase()
        await refreshData()
        alert('تم إعادة تعيين قاعدة البيانات بنجاح!')
        window.location.reload()
      } catch (error) {
        alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات')
        console.error(error)
      }
    }
  }

  const stats = [
    {
      title: 'إجمالي المطابخ',
      value: kitchensData?.length || 0,
      icon: 'ri-home-4-line',
      color: 'blue',
      trend: { type: 'up', value: '+12%' }
    },
    {
      title: 'إجمالي الخزائن',
      value: cabinetsData?.length || 0,
      icon: 'ri-archive-drawer-line',
      color: 'purple',
      trend: { type: 'up', value: '+8%' }
    },
    {
      title: 'المميزات',
      value: whyChooseUsData?.features?.length || 0,
      icon: 'ri-star-smile-line',
      color: 'green',
      trend: { type: 'stable', value: '0%' }
    },
    {
      title: 'إجمالي الصور',
      value: (kitchensData?.reduce((acc, kitchen) => acc + (kitchen.images?.length || 0), 0) || 0) +
             (cabinetsData?.reduce((acc, cabinet) => acc + (cabinet.images?.length || 0), 0) || 0),
      icon: 'ri-image-2-line',
      color: 'orange',
      trend: { type: 'up', value: '+25%' }
    }
  ]

  const quickActions = [
    {
      title: 'إضافة مطبخ جديد',
      description: 'أضف تصميم مطبخ جديد إلى المعرض',
      icon: 'ri-add-line',
      color: 'from-blue-500 to-blue-600',
      action: 'kitchens'
    },
    {
      title: 'إضافة خزانة جديدة',
      description: 'أضف تصميم خزانة جديد إلى المعرض',
      icon: 'ri-add-line',
      color: 'from-purple-500 to-purple-600',
      action: 'cabinets'
    },
    {
      title: 'تحديث الهيرو',
      description: 'عدّل النصوص والصور في القسم الرئيسي',
      icon: 'ri-edit-line',
      color: 'from-green-500 to-green-600',
      action: 'hero'
    },
    {
      title: 'إدارة المميزات',
      description: 'أضف أو عدّل مميزات الشركة',
      icon: 'ri-settings-line',
      color: 'from-orange-500 to-orange-600',
      action: 'why-choose-us'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <AnimatedCard className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-3xl p-8 text-white shadow-2xl">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
              <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <i className="ri-dashboard-3-line text-2xl text-white"></i>
              </div>
              <div>
                <h1 className="text-2xl md:text-3xl font-bold">مرحباً بك في لوحة التحكم</h1>
                <p className="text-blue-100 text-sm md:text-lg">إدارة محتوى موقع عجائب الخبراء</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-100">النظام يعمل بشكل طبيعي</span>
              </div>
              <div className="hidden md:flex items-center space-x-2 rtl:space-x-reverse">
                <i className="ri-time-line text-blue-200"></i>
                <span className="text-blue-200">{new Date().toLocaleString('ar-SA')}</span>
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="w-24 h-24 bg-white/10 rounded-3xl flex items-center justify-center backdrop-blur-sm border border-white/20">
              <i className="ri-admin-line text-4xl text-white"></i>
            </div>
          </div>
        </div>
      </AnimatedCard>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            trend={stat.trend}
            delay={index * 100}
          />
        ))}
      </div>

      {/* Quick Actions */}
      <AnimatedCard className="bg-white rounded-3xl p-6 shadow-sm border border-gray-200/50">
        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-6">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
            <i className="ri-flashlight-line text-white text-lg"></i>
          </div>
          <h2 className="text-xl font-bold text-gray-900">إجراءات سريعة</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <AnimatedCard
              key={index}
              className="p-6 border border-gray-200/50 rounded-2xl hover:border-blue-300 hover:shadow-lg text-right group bg-white"
              delay={index * 100}
            >
              <div className={`w-12 h-12 bg-gradient-to-br ${action.color} rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg`}>
                <i className={`${action.icon} text-xl text-white`}></i>
              </div>
              <h3 className="font-bold text-gray-900 mb-2">{action.title}</h3>
              <p className="text-sm text-gray-600 leading-relaxed">{action.description}</p>
              <div className="mt-4 flex items-center text-blue-600 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                <span>انقر للبدء</span>
                <i className="ri-arrow-left-s-line mr-1"></i>
              </div>
            </AnimatedCard>
          ))}
        </div>
      </AnimatedCard>

      {/* Recent Activity */}
      <AnimatedCard className="bg-white rounded-3xl p-6 shadow-sm border border-gray-200/50">
        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-6">
          <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center">
            <i className="ri-history-line text-white text-lg"></i>
          </div>
          <h2 className="text-xl font-bold text-gray-900">النشاط الأخير</h2>
        </div>
        <div className="space-y-4">
          <div className="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-2xl border border-blue-200/50">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
              <i className="ri-login-box-line text-white"></i>
            </div>
            <div className="flex-1">
              <p className="text-sm font-bold text-gray-900">تم تسجيل الدخول بنجاح</p>
              <p className="text-xs text-gray-600 font-medium">{new Date().toLocaleString('ar-SA')}</p>
            </div>
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          </div>
          <div className="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gradient-to-r from-emerald-50 to-emerald-100/50 rounded-2xl border border-emerald-200/50">
            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
              <i className="ri-check-line text-white"></i>
            </div>
            <div className="flex-1">
              <p className="text-sm font-bold text-gray-900">تم تحميل البيانات بنجاح</p>
              <p className="text-xs text-gray-600 font-medium">منذ دقائق قليلة</p>
            </div>
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          </div>
          <div className="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gradient-to-r from-purple-50 to-purple-100/50 rounded-2xl border border-purple-200/50">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
              <i className="ri-database-2-line text-white"></i>
            </div>
            <div className="flex-1">
              <p className="text-sm font-bold text-gray-900">تم تحديث قاعدة البيانات</p>
              <p className="text-xs text-gray-600 font-medium">منذ ساعة</p>
            </div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </AnimatedCard>

      {/* System Info */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AnimatedCard className="bg-white rounded-3xl shadow-sm border border-gray-200/50 p-6">
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center">
              <i className="ri-settings-3-line text-white text-lg"></i>
            </div>
            <h3 className="text-lg font-bold text-gray-900">معلومات النظام</h3>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-xl">
              <span className="text-gray-600 font-medium">إصدار النظام:</span>
              <span className="font-bold text-gray-900 bg-blue-100 text-blue-800 px-3 py-1 rounded-lg text-sm">v2.0.0</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-xl">
              <span className="text-gray-600 font-medium">آخر تحديث:</span>
              <span className="font-bold text-gray-900">اليوم</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-xl">
              <span className="text-gray-600 font-medium">حالة النظام:</span>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-green-600 font-bold">متصل</span>
              </div>
            </div>
          </div>
          <div className="mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={handleResetDatabase}
              className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white py-3 px-4 rounded-2xl transition-all duration-300 text-sm font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
            >
              <i className="ri-refresh-line mr-2"></i>
              إعادة تعيين قاعدة البيانات
            </button>
          </div>
        </AnimatedCard>

        <AnimatedCard className="bg-white rounded-3xl shadow-sm border border-gray-200/50 p-6" delay={200}>
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center">
              <i className="ri-lightbulb-line text-white text-lg"></i>
            </div>
            <h3 className="text-lg font-bold text-gray-900">نصائح سريعة</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-start space-x-3 rtl:space-x-reverse p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl border border-yellow-200/50">
              <div className="w-8 h-8 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-0.5">
                <i className="ri-image-line text-white text-sm"></i>
              </div>
              <span className="text-sm text-gray-700 font-medium leading-relaxed">استخدم صور عالية الجودة للحصول على أفضل النتائج</span>
            </div>
            <div className="flex items-start space-x-3 rtl:space-x-reverse p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200/50">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-0.5">
                <i className="ri-edit-line text-white text-sm"></i>
              </div>
              <span className="text-sm text-gray-700 font-medium leading-relaxed">احرص على كتابة أوصاف واضحة ومفيدة</span>
            </div>
            <div className="flex items-start space-x-3 rtl:space-x-reverse p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl border border-emerald-200/50">
              <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-0.5">
                <i className="ri-save-line text-white text-sm"></i>
              </div>
              <span className="text-sm text-gray-700 font-medium leading-relaxed">قم بحفظ التغييرات بانتظام</span>
            </div>
          </div>
        </AnimatedCard>
      </div>
    </div>
  )
}

export default DashboardHome
