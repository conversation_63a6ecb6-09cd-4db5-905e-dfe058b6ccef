import { useState, useEffect } from 'react'
import { useAuth } from '../context/AuthContext'
import MobileNavigation from './common/MobileNavigation'
import HeroManagement from './sections/HeroManagement'
import WhyChooseUsManagement from './sections/WhyChooseUsManagement'
import KitchensManagement from './sections/KitchensManagement'
import CabinetsManagement from './sections/CabinetsManagement'
import FooterManagement from './sections/FooterManagement'
import UserManagement from './sections/UserManagement'
import DashboardHome from './sections/DashboardHome'

const Dashboard = () => {
  const [activeSection, setActiveSection] = useState('dashboard')
  const [contentVisible, setContentVisible] = useState(false)
  const { user, logout } = useAuth()

  // Effect to trigger content animation when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setContentVisible(true)
    }, 100)
    return () => clearTimeout(timer)
  }, [])

  // Effect to trigger content animation when section changes
  useEffect(() => {
    setContentVisible(false)
    const timer = setTimeout(() => {
      setContentVisible(true)
    }, 50)
    return () => clearTimeout(timer)
  }, [activeSection])

  const menuItems = [
    {
      id: 'dashboard',
      name: 'الرئيسية',
      icon: 'ri-dashboard-3-line',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      description: 'نظرة عامة'
    },
    {
      id: 'hero',
      name: 'الهيرو',
      icon: 'ri-image-2-line',
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      description: 'القسم الرئيسي'
    },
    {
      id: 'why-choose-us',
      name: 'مميزاتنا',
      icon: 'ri-star-smile-line',
      color: 'from-emerald-500 to-emerald-600',
      bgColor: 'bg-emerald-50',
      description: 'لماذا تختارنا'
    },
    {
      id: 'kitchens',
      name: 'المطابخ',
      icon: 'ri-home-4-line',
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50',
      description: 'معرض المطابخ'
    },
    {
      id: 'cabinets',
      name: 'الخزائن',
      icon: 'ri-archive-drawer-line',
      color: 'from-teal-500 to-teal-600',
      bgColor: 'bg-teal-50',
      description: 'معرض الخزائن'
    },
    {
      id: 'footer',
      name: 'الفوتر',
      icon: 'ri-links-line',
      color: 'from-indigo-500 to-indigo-600',
      bgColor: 'bg-indigo-50',
      description: 'معلومات التواصل'
    },
    {
      id: 'users',
      name: 'المستخدمين',
      icon: 'ri-user-settings-line',
      color: 'from-pink-500 to-pink-600',
      bgColor: 'bg-pink-50',
      description: 'إدارة الحسابات'
    }
  ]

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return <DashboardHome />
      case 'hero':
        return <HeroManagement />
      case 'why-choose-us':
        return <WhyChooseUsManagement />
      case 'kitchens':
        return <KitchensManagement />
      case 'cabinets':
        return <CabinetsManagement />
      case 'footer':
        return <FooterManagement />
      case 'users':
        return <UserManagement />
      default:
        return <DashboardHome />
    }
  }

  const handleLogout = () => {
    if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      logout()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/50 to-indigo-50/50 relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%)`
        }}></div>
      </div>

      {/* Modern Header */}
      <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-sm">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo & Title */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                <i className="ri-admin-line text-white text-lg"></i>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-gray-900">لوحة التحكم</h1>
                <p className="text-sm text-gray-500">عجائب الخبراء</p>
              </div>
            </div>

            {/* User Info & Actions */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              {/* Notifications */}
              <button className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <i className="ri-notification-line text-xl"></i>
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
              </button>

              {/* User Menu */}
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">
                    {user?.username?.charAt(0)?.toUpperCase() || 'A'}
                  </span>
                </div>
                <div className="hidden md:block text-right">
                  <p className="text-sm font-medium text-gray-900">{user?.username || 'المدير'}</p>
                  <p className="text-xs text-gray-500">مدير النظام</p>
                </div>
                <button
                  onClick={handleLogout}
                  className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  title="تسجيل الخروج"
                >
                  <i className="ri-logout-box-line text-lg"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10">
        {/* Desktop Navigation Cards */}
        <div className="hidden md:block p-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {menuItems.map((item, index) => (
                <button
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className={`
                    group relative p-6 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1
                    ${activeSection === item.id
                      ? `bg-gradient-to-br ${item.color} text-white shadow-xl shadow-${item.color.split('-')[1]}-500/25`
                      : `${item.bgColor} hover:bg-white text-gray-700 hover:shadow-lg border border-gray-200/50`
                    }
                  `}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="flex flex-col items-center text-center space-y-3">
                    <div className={`
                      w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300
                      ${activeSection === item.id
                        ? 'bg-white/20'
                        : `bg-gradient-to-br ${item.color} text-white group-hover:scale-110`
                      }
                    `}>
                      <i className={`${item.icon} text-xl`}></i>
                    </div>
                    <div>
                      <h3 className="font-bold text-lg mb-1">{item.name}</h3>
                      <p className={`text-sm ${activeSection === item.id ? 'text-white/80' : 'text-gray-500'}`}>
                        {item.description}
                      </p>
                    </div>
                  </div>

                  {activeSection === item.id && (
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-2xl"></div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="px-4 sm:px-6 lg:px-8 pb-20 md:pb-8">
          <div className="max-w-7xl mx-auto">
            <div className={`transition-all duration-500 ${contentVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              {renderActiveSection()}
            </div>
          </div>
        </div>
      </main>

      {/* Mobile Bottom Navigation */}
      <MobileNavigation
        activeSection={activeSection}
        setActiveSection={setActiveSection}
        menuItems={menuItems}
      />
    </div>
  )
}

export default Dashboard
