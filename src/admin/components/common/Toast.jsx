import { useState, useEffect } from 'react'

const Toast = ({ 
  message, 
  type = 'success', 
  duration = 3000, 
  onClose,
  position = 'top-right'
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)

  useEffect(() => {
    // Show animation
    const showTimer = setTimeout(() => setIsVisible(true), 100)
    
    // Auto hide
    const hideTimer = setTimeout(() => {
      setIsLeaving(true)
      setTimeout(() => {
        onClose && onClose()
      }, 300)
    }, duration)

    return () => {
      clearTimeout(showTimer)
      clearTimeout(hideTimer)
    }
  }, [duration, onClose])

  const typeStyles = {
    success: {
      bg: 'from-emerald-500 to-emerald-600',
      icon: 'ri-check-line',
      border: 'border-emerald-200'
    },
    error: {
      bg: 'from-red-500 to-red-600',
      icon: 'ri-close-line',
      border: 'border-red-200'
    },
    warning: {
      bg: 'from-yellow-500 to-orange-500',
      icon: 'ri-alert-line',
      border: 'border-yellow-200'
    },
    info: {
      bg: 'from-blue-500 to-blue-600',
      icon: 'ri-information-line',
      border: 'border-blue-200'
    }
  }

  const positionStyles = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
  }

  const style = typeStyles[type]

  return (
    <div className={`
      fixed z-50 ${positionStyles[position]}
      transition-all duration-300 ease-out
      ${isVisible && !isLeaving 
        ? 'opacity-100 translate-y-0 scale-100' 
        : 'opacity-0 translate-y-2 scale-95'
      }
    `}>
      <div className={`
        bg-white rounded-2xl shadow-2xl border ${style.border}
        p-4 min-w-[300px] max-w-md
        backdrop-blur-xl
      `}>
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className={`
            w-10 h-10 rounded-2xl bg-gradient-to-br ${style.bg}
            flex items-center justify-center shadow-lg
          `}>
            <i className={`${style.icon} text-white text-lg`}></i>
          </div>
          
          <div className="flex-1">
            <p className="text-gray-900 font-medium text-sm leading-relaxed">
              {message}
            </p>
          </div>
          
          <button
            onClick={() => {
              setIsLeaving(true)
              setTimeout(() => onClose && onClose(), 300)
            }}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-lg hover:bg-gray-100"
          >
            <i className="ri-close-line text-sm"></i>
          </button>
        </div>
        
        {/* Progress bar */}
        <div className="mt-3 w-full h-1 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className={`h-full bg-gradient-to-r ${style.bg} rounded-full transition-all ease-linear`}
            style={{
              animation: `shrink ${duration}ms linear forwards`
            }}
          ></div>
        </div>
      </div>
    </div>
  )
}

// Toast container component
export const ToastContainer = ({ toasts, removeToast }) => {
  return (
    <>
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          duration={toast.duration}
          position={toast.position}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </>
  )
}

export default Toast
