import { useState, useEffect } from 'react'

const MobileNavigation = ({ activeSection, setActiveSection, menuItems }) => {
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false)
      } else {
        setIsVisible(true)
      }
      
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  const handleSectionChange = (sectionId) => {
    setActiveSection(sectionId)
    // Add haptic feedback for mobile devices
    if (navigator.vibrate) {
      navigator.vibrate(50)
    }
  }

  return (
    <nav className={`
      md:hidden fixed bottom-0 left-0 right-0 z-50
      bg-white/95 backdrop-blur-xl border-t border-gray-200/50 shadow-2xl
      transition-transform duration-300 ease-in-out
      ${isVisible ? 'translate-y-0' : 'translate-y-full'}
    `}>
      {/* Primary Navigation Row */}
      <div className="grid grid-cols-4 gap-1 p-2">
        {menuItems.slice(0, 4).map((item, index) => (
          <button
            key={item.id}
            onClick={() => handleSectionChange(item.id)}
            className={`
              relative flex flex-col items-center justify-center p-3 rounded-2xl 
              transition-all duration-300 transform active:scale-95
              ${activeSection === item.id 
                ? `bg-gradient-to-br ${item.color} text-white shadow-lg shadow-${item.color.split('-')[1]}-500/25` 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }
            `}
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {/* Active indicator */}
            {activeSection === item.id && (
              <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"></div>
            )}
            
            <i className={`${item.icon} text-lg mb-1 transition-transform duration-300 ${
              activeSection === item.id ? 'scale-110' : ''
            }`}></i>
            <span className="text-xs font-bold leading-tight text-center">{item.name}</span>
            
            {/* Ripple effect */}
            <div className="absolute inset-0 rounded-2xl overflow-hidden">
              <div className="absolute inset-0 bg-white/20 scale-0 rounded-2xl transition-transform duration-300 active:scale-100"></div>
            </div>
          </button>
        ))}
      </div>
      
      {/* Secondary Navigation Row */}
      <div className="grid grid-cols-3 gap-1 p-2 pt-0">
        {menuItems.slice(4).map((item, index) => (
          <button
            key={item.id}
            onClick={() => handleSectionChange(item.id)}
            className={`
              relative flex flex-col items-center justify-center p-3 rounded-2xl 
              transition-all duration-300 transform active:scale-95
              ${activeSection === item.id 
                ? `bg-gradient-to-br ${item.color} text-white shadow-lg shadow-${item.color.split('-')[1]}-500/25` 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }
            `}
            style={{ animationDelay: `${(index + 4) * 50}ms` }}
          >
            {/* Active indicator */}
            {activeSection === item.id && (
              <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"></div>
            )}
            
            <i className={`${item.icon} text-lg mb-1 transition-transform duration-300 ${
              activeSection === item.id ? 'scale-110' : ''
            }`}></i>
            <span className="text-xs font-bold leading-tight text-center">{item.name}</span>
            
            {/* Ripple effect */}
            <div className="absolute inset-0 rounded-2xl overflow-hidden">
              <div className="absolute inset-0 bg-white/20 scale-0 rounded-2xl transition-transform duration-300 active:scale-100"></div>
            </div>
          </button>
        ))}
      </div>

      {/* Safe area for devices with home indicator */}
      <div className="h-safe-area-inset-bottom"></div>
    </nav>
  )
}

export default MobileNavigation
