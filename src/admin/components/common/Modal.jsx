import { useEffect } from 'react'

const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'lg',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true
}) => {

  useEffect(() => {
    if (isOpen) {
      // منع التمرير في الخلفية
      document.body.style.overflow = 'hidden'

      // معالجة مفتاح Escape
      const handleEscape = (e) => {
        if (e.key === 'Escape' && closeOnEscape) {
          onClose()
        }
      }

      if (closeOnEscape) {
        document.addEventListener('keydown', handleEscape)
      }

      return () => {
        document.removeEventListener('keydown', handleEscape)
        document.body.style.overflow = 'unset'
      }
    }
  }, [isOpen, onClose, closeOnEscape])

  if (!isOpen) return null

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && closeOnOverlayClick) {
      onClose()
    }
  }

  return (
    <div
      className="fixed inset-0 z-[999999] flex items-center justify-center p-4"
      onClick={handleOverlayClick}
    >
      <div
        className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            {title && (
              <h2 className="text-xl font-bold text-gray-900">{title}</h2>
            )}
            {showCloseButton && (
              <button
                onClick={onClose}
                className="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors duration-200 ml-auto"
                aria-label="إغلاق"
              >
                <i className="ri-close-line text-gray-600 text-lg"></i>
              </button>
            )}
          </div>
        )}
        
        {/* Content */}
        <div className="modal-body">
          {children}
        </div>
      </div>
    </div>
  )
}

export default Modal
