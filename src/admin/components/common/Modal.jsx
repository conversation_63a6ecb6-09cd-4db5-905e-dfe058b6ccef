import { useEffect } from 'react'

const Modal = ({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  size = 'lg',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true 
}) => {
  
  useEffect(() => {
    if (isOpen) {
      // منع التمرير في الخلفية
      document.body.classList.add('modal-open')
      
      // معالجة مفتاح Escape
      const handleEscape = (e) => {
        if (e.key === 'Escape' && closeOnEscape) {
          onClose()
        }
      }
      
      if (closeOnEscape) {
        document.addEventListener('keydown', handleEscape)
      }
      
      return () => {
        document.removeEventListener('keydown', handleEscape)
      }
    }
    
    return () => {
      document.body.classList.remove('modal-open')
    }
  }, [isOpen, onClose, closeOnEscape])

  if (!isOpen) return null

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-7xl'
  }

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && closeOnOverlayClick) {
      onClose()
    }
  }

  return (
    <div 
      className="modal-overlay"
      onClick={handleOverlayClick}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 999999,
        padding: '20px',
        backdropFilter: 'blur(12px)',
        overflow: 'auto',
        animation: 'modalOverlayFadeIn 0.3s ease-out'
      }}
    >
      <div 
        className={`modal-content ${sizeClasses[size]}`}
        onClick={(e) => e.stopPropagation()}
        style={{
          background: 'white',
          borderRadius: '24px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.6)',
          width: '95%',
          maxHeight: '90vh',
          overflowY: 'auto',
          animation: 'modalSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
          position: 'relative',
          transform: 'translateZ(0)',
          margin: '0 auto',
          border: '2px solid rgba(255, 255, 255, 0.2)'
        }}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            {title && (
              <h2 className="text-xl font-bold text-gray-900">{title}</h2>
            )}
            {showCloseButton && (
              <button
                onClick={onClose}
                className="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors duration-200 ml-auto"
                aria-label="إغلاق"
              >
                <i className="ri-close-line text-gray-600 text-lg"></i>
              </button>
            )}
          </div>
        )}
        
        {/* Content */}
        <div className="modal-body">
          {children}
        </div>
      </div>
    </div>
  )
}

export default Modal
