const LoadingSpinner = ({ size = 'md', color = 'blue', text = 'جاري التحميل...' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const colorClasses = {
    blue: 'border-blue-500',
    purple: 'border-purple-500',
    green: 'border-emerald-500',
    orange: 'border-orange-500',
    red: 'border-red-500'
  }

  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      <div className="relative">
        {/* Main spinner */}
        <div className={`
          ${sizeClasses[size]} border-4 border-gray-200 rounded-full
          animate-spin ${colorClasses[color]}
        `} style={{
          borderTopColor: 'transparent',
          borderRightColor: 'transparent'
        }}></div>
        
        {/* Inner spinner */}
        <div className={`
          absolute inset-2 border-2 border-gray-100 rounded-full
          animate-spin ${colorClasses[color]} opacity-60
        `} style={{
          borderTopColor: 'transparent',
          borderLeftColor: 'transparent',
          animationDirection: 'reverse',
          animationDuration: '0.8s'
        }}></div>
      </div>
      
      {text && (
        <p className="text-sm text-gray-600 font-medium animate-pulse">{text}</p>
      )}
    </div>
  )
}

export default LoadingSpinner
