import { useState, useEffect } from 'react'
import AnimatedCard from './AnimatedCard'

const StatsCard = ({ 
  title, 
  value, 
  icon, 
  color = 'blue', 
  trend = null,
  delay = 0,
  prefix = '',
  suffix = ''
}) => {
  const [animatedValue, setAnimatedValue] = useState(0)

  useEffect(() => {
    const timer = setTimeout(() => {
      const duration = 1000
      const steps = 60
      const increment = value / steps
      let current = 0

      const counter = setInterval(() => {
        current += increment
        if (current >= value) {
          setAnimatedValue(value)
          clearInterval(counter)
        } else {
          setAnimatedValue(Math.floor(current))
        }
      }, duration / steps)

      return () => clearInterval(counter)
    }, delay)

    return () => clearTimeout(timer)
  }, [value, delay])

  const colorClasses = {
    blue: {
      bg: 'from-blue-500 to-blue-600',
      text: 'text-blue-600',
      bgLight: 'bg-blue-50',
      ring: 'ring-blue-500/20'
    },
    green: {
      bg: 'from-emerald-500 to-emerald-600',
      text: 'text-emerald-600',
      bgLight: 'bg-emerald-50',
      ring: 'ring-emerald-500/20'
    },
    purple: {
      bg: 'from-purple-500 to-purple-600',
      text: 'text-purple-600',
      bgLight: 'bg-purple-50',
      ring: 'ring-purple-500/20'
    },
    orange: {
      bg: 'from-orange-500 to-orange-600',
      text: 'text-orange-600',
      bgLight: 'bg-orange-50',
      ring: 'ring-orange-500/20'
    },
    red: {
      bg: 'from-red-500 to-red-600',
      text: 'text-red-600',
      bgLight: 'bg-red-50',
      ring: 'ring-red-500/20'
    }
  }

  const colors = colorClasses[color] || colorClasses.blue

  return (
    <AnimatedCard 
      className={`p-6 bg-white rounded-2xl border border-gray-200/50 shadow-sm hover:shadow-lg ${colors.ring} hover:ring-4`}
      delay={delay}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-2">{title}</p>
          <div className="flex items-baseline space-x-2 rtl:space-x-reverse">
            <span className="text-3xl font-bold text-gray-900">
              {prefix}{animatedValue.toLocaleString('ar-SA')}{suffix}
            </span>
            {trend && (
              <span className={`
                inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                ${trend.type === 'up' 
                  ? 'bg-green-100 text-green-800' 
                  : trend.type === 'down' 
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-800'
                }
              `}>
                {trend.type === 'up' && <i className="ri-arrow-up-line mr-1"></i>}
                {trend.type === 'down' && <i className="ri-arrow-down-line mr-1"></i>}
                {trend.value}
              </span>
            )}
          </div>
        </div>
        
        <div className={`
          w-16 h-16 rounded-2xl bg-gradient-to-br ${colors.bg} 
          flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-110
        `}>
          <i className={`${icon} text-2xl text-white`}></i>
        </div>
      </div>
      
      {/* Progress Bar */}
      <div className="mt-4">
        <div className={`w-full h-2 ${colors.bgLight} rounded-full overflow-hidden`}>
          <div 
            className={`h-full bg-gradient-to-r ${colors.bg} rounded-full transition-all duration-1000 ease-out`}
            style={{ 
              width: `${Math.min((animatedValue / value) * 100, 100)}%`,
              transitionDelay: `${delay + 500}ms`
            }}
          ></div>
        </div>
      </div>
    </AnimatedCard>
  )
}

export default StatsCard
