import { useState } from 'react'

const AnimatedCard = ({ 
  children, 
  className = '', 
  onClick, 
  isActive = false,
  delay = 0,
  hoverEffect = true,
  rippleEffect = true 
}) => {
  const [ripples, setRipples] = useState([])

  const handleClick = (e) => {
    if (rippleEffect) {
      const rect = e.currentTarget.getBoundingClientRect()
      const size = Math.max(rect.width, rect.height)
      const x = e.clientX - rect.left - size / 2
      const y = e.clientY - rect.top - size / 2
      
      const newRipple = {
        x,
        y,
        size,
        id: Date.now()
      }
      
      setRipples(prev => [...prev, newRipple])
      
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id))
      }, 600)
    }
    
    if (onClick) {
      onClick(e)
    }
  }

  return (
    <div
      className={`
        admin-card relative overflow-hidden cursor-pointer
        transition-all duration-300 ease-out
        ${hoverEffect ? 'hover:shadow-xl hover:-translate-y-2' : ''}
        ${isActive ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
        ${className}
      `}
      onClick={handleClick}
      style={{ 
        animationDelay: `${delay}ms`,
        animationFillMode: 'both'
      }}
    >
      {children}
      
      {/* Ripple Effect */}
      {ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute bg-white/30 rounded-full pointer-events-none animate-ping"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
            animationDuration: '600ms'
          }}
        />
      ))}
      
      {/* Shine Effect */}
      {hoverEffect && (
        <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-700"></div>
        </div>
      )}
    </div>
  )
}

export default AnimatedCard
