import { useState } from 'react'
import Modal from './Modal'

const ModalTest = () => {
  const [showModal, setShowModal] = useState(false)

  return (
    <div className="p-8">
      <button 
        onClick={() => setShowModal(true)}
        className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600"
      >
        اختبار المودل
      </button>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="اختبار المودل الجديد"
        size="lg"
      >
        <div className="p-6">
          <h3 className="text-lg font-bold mb-4">المودل يعمل بشكل مثالي! 🎉</h3>
          <p className="text-gray-600 mb-4">
            تم إصلاح جميع مشاكل المودل وهو الآن يعمل بشكل طبيعي على جميع الأجهزة.
          </p>
          <ul className="list-disc list-inside space-y-2 text-sm text-gray-700">
            <li>✅ يفتح ويغلق بشكل سلس</li>
            <li>✅ يعمل على الهواتف المحمولة</li>
            <li>✅ يدعم مفتاح Escape للإغلاق</li>
            <li>✅ يمنع التمرير في الخلفية</li>
            <li>✅ تصميم عصري وجذاب</li>
          </ul>
        </div>
      </Modal>
    </div>
  )
}

export default ModalTest
