/* Modern Dashboard Styles */

/* Smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Card hover effects */
.admin-card {
  animation: fadeInUp 0.6s ease-out forwards;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.admin-card:active {
  transform: translateY(-4px) scale(0.98);
}

/* Icon animations */
.admin-icon {
  transition: all 0.3s ease;
}

.admin-card:hover .admin-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .admin-card {
    padding: 1rem;
  }

  .admin-card h3 {
    font-size: 1rem;
  }

  .admin-card p {
    font-size: 0.875rem;
  }

  /* Mobile-specific spacing */
  .space-y-8 > * + * {
    margin-top: 1.5rem !important;
  }

  /* Mobile header adjustments */
  .mobile-header {
    padding: 1rem;
  }

  /* Mobile stats grid */
  .mobile-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  /* Mobile quick actions */
  .mobile-actions {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .tablet-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .tablet-spacing {
    padding: 1.5rem;
  }
}

/* Large screen optimizations */
@media (min-width: 1440px) {
  .large-container {
    max-width: 1400px;
    margin: 0 auto;
  }

  .large-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Bottom navigation animations */
.bottom-nav-item {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.bottom-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.bottom-nav-item:active::before {
  left: 100%;
}

/* Ripple effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::after {
  width: 300px;
  height: 300px;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes shrink {
  0% {
    width: 100%;
  }
  100% {
    width: 0%;
  }
}

/* Toast animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Focus states for accessibility */
.admin-card:focus,
.bottom-nav-item:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .glass {
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Print styles */
@media print {
  .bottom-nav,
  .admin-card {
    display: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .admin-card {
    border: 2px solid #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .admin-card:hover {
    transform: none;
  }

  .admin-card:active {
    transform: scale(0.95);
  }
}

/* Modal improvements for new dashboard */
.modal-overlay {
  z-index: 999999 !important;
}

.modal-content {
  z-index: 1000000 !important;
}

/* Ensure modal appears above everything */
body.modal-open .modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 999999 !important;
}

/* Landscape mobile optimization */
@media screen and (max-height: 500px) and (orientation: landscape) {
  .bottom-nav {
    padding: 0.5rem;
  }
  
  .bottom-nav-item {
    padding: 0.5rem;
  }
}
