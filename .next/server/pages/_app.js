(()=>{var a={};a.id=636,a.ids=[636],a.modules={2015:a=>{"use strict";a.exports=require("react")},6539:(a,b,c)=>{"use strict";c.d(b,{bv:()=>e,i3:()=>d});let d={baseURL:"http://localhost:3003",uploadsURL:"/uploads"},e=a=>a?(a.startsWith("http://")||a.startsWith("https://"),a):null},6745:(a,b,c)=>{"use strict";c.d(b,{$L:()=>w,Cm:()=>k,It:()=>j,KT:()=>i,OX:()=>p,WH:()=>m,aU:()=>h,bW:()=>q,dt:()=>u,ro:()=>s,sB:()=>o,t1:()=>v,ub:()=>r,wf:()=>t,y9:()=>l,yI:()=>n,zm:()=>g});let d=c(6539).i3.baseURL,e=async(a,b={})=>{try{let c=await fetch(`${d}${a}`,{headers:{"Content-Type":"application/json",...b.headers},...b});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(a){throw a}},f=(a=500)=>new Promise(b=>setTimeout(b,a)),g=async()=>{try{return await f(),await e("/api/hero")}catch(a){throw a}},h=async(a,b=null)=>{try{await f();let b=await e("/api/hero"),c=b?.id||1;return await e(`/api/hero/${c}`,{method:"PUT",body:JSON.stringify(a)})}catch(a){throw a}},i=async()=>{try{return await f(),await e("/api/kitchens")}catch(a){throw a}},j=async(a,b=null)=>{try{return await f(),await e("/api/kitchens",{method:"POST",body:JSON.stringify(a)})}catch(a){throw a}},k=async(a,b,c=null)=>{try{return await f(),await e(`/api/kitchens/${a}`,{method:"PUT",body:JSON.stringify(b)})}catch(a){throw a}},l=async(a,b=null)=>{try{return await f(),await e(`/api/kitchens/${a}`,{method:"DELETE"})}catch(a){throw a}},m=async()=>{try{return await f(),await e("/api/cabinets")}catch(a){throw a}},n=async(a,b=null)=>{try{return await f(),await e("/api/cabinets",{method:"POST",body:JSON.stringify(a)})}catch(a){throw a}},o=async(a,b,c=null)=>{try{return await f(),await e(`/api/cabinets/${a}`,{method:"PUT",body:JSON.stringify(b)})}catch(a){throw a}},p=async(a,b=null)=>{try{return await f(),await e(`/api/cabinets/${a}`,{method:"DELETE"})}catch(a){throw a}},q=async()=>{try{return await f(),await e("/api/categories")}catch(a){throw a}},r=async()=>{try{return await f(),await e("/api/why-choose-us")}catch(a){throw a}},s=async(a,b=null)=>{try{return await f(),await e("/api/why-choose-us",{method:"PUT",body:JSON.stringify(a)})}catch(a){throw a}},t=async()=>{try{return await f(),await e("/api/footer")}catch(a){throw a}},u=async(a,b=null)=>{try{return await f(),await e("/api/footer",{method:"PUT",body:JSON.stringify(a)})}catch(a){throw a}},v=async()=>{try{return await f(),await e("/api/company-settings")}catch(a){throw a}},w=async(a,b,c=null)=>{try{return await f(),{success:!0}}catch(a){throw a}}},7522:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(8732);function e({Component:a,pageProps:b}){return(0,d.jsx)("div",{dir:"rtl",children:(0,d.jsx)(a,{...b})})}c(8754),c(2015),c(8015)},8015:(a,b,c)=>{"use strict";c.d(b,{aB:()=>f,wU:()=>e});var d=c(6745);let e=async()=>{try{let a=new Promise((a,b)=>{setTimeout(()=>b(Error("Database initialization timeout after 30 seconds")),3e4)}),b=Promise.all([(0,d.zm)().catch(a=>null),(0,d.ub)().catch(a=>null),(0,d.KT)().catch(a=>[]),(0,d.WH)().catch(a=>[]),(0,d.wf)().catch(a=>null),(0,d.bW)().catch(a=>[]),(0,d.t1)().catch(a=>({}))]);if((await Promise.race([b,a])).filter(a=>null!=a).length>0)return!0;return!1}catch(a){return!1}},f=async()=>{try{return await e(),!0}catch(a){return!1}}},8732:a=>{"use strict";a.exports=require("react/jsx-runtime")},8754:()=>{}};var b=require("../webpack-runtime.js");b.C(a);var c=b(b.s=7522);module.exports=c})();