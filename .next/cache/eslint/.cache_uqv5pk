[{"/var/www/html/pages/_app.js": "1", "/var/www/html/pages/_document.js": "2", "/var/www/html/pages/admin.js": "3", "/var/www/html/pages/api/[...slug].js": "4", "/var/www/html/pages/api/cabinets.js": "5", "/var/www/html/pages/api/footer.js": "6", "/var/www/html/pages/api/hero.js": "7", "/var/www/html/pages/api/kitchens.js": "8", "/var/www/html/pages/api/why-choose-us.js": "9", "/var/www/html/pages/cabinets.js": "10", "/var/www/html/pages/index.js": "11", "/var/www/html/pages/kitchens.js": "12", "/var/www/html/src/admin/components/Dashboard.jsx": "13", "/var/www/html/src/admin/components/Header.jsx": "14", "/var/www/html/src/admin/components/Login.jsx": "15", "/var/www/html/src/admin/components/Sidebar.jsx": "16", "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx": "17", "/var/www/html/src/admin/components/sections/DashboardHome.jsx": "18", "/var/www/html/src/admin/components/sections/FooterManagement.jsx": "19", "/var/www/html/src/admin/components/sections/HeroManagement.jsx": "20", "/var/www/html/src/admin/components/sections/KitchensManagement.jsx": "21", "/var/www/html/src/admin/components/sections/UserManagement.jsx": "22", "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx": "23", "/var/www/html/src/admin/context/AuthContext.jsx": "24", "/var/www/html/src/admin/context/DataContext.jsx": "25", "/var/www/html/src/admin/utils/initDatabase.js": "26", "/var/www/html/src/components/CabinetGallery.jsx": "27", "/var/www/html/src/components/CallToAction.jsx": "28", "/var/www/html/src/components/Footer.jsx": "29", "/var/www/html/src/components/HeroSection.jsx": "30", "/var/www/html/src/components/KitchenGallery.jsx": "31", "/var/www/html/src/components/Navbar.jsx": "32", "/var/www/html/src/components/SEO.jsx": "33", "/var/www/html/src/components/Testimonials.jsx": "34", "/var/www/html/src/components/WhyChooseUs.jsx": "35", "/var/www/html/src/config/api.js": "36", "/var/www/html/src/config/env.js": "37", "/var/www/html/src/components/mobile/MobileAbout.jsx": "38", "/var/www/html/src/components/mobile/MobileBottomNav.jsx": "39", "/var/www/html/src/components/mobile/MobileCabinets.jsx": "40", "/var/www/html/src/components/mobile/MobileContact.jsx": "41", "/var/www/html/src/components/mobile/MobileHeader.jsx": "42", "/var/www/html/src/components/mobile/MobileHome.jsx": "43", "/var/www/html/src/components/mobile/MobileKitchens.jsx": "44", "/var/www/html/src/components/mobile/MobileLayout.jsx": "45", "/var/www/html/src/components/mobile/MobileProductModal.jsx": "46", "/var/www/html/src/utils/performance.js": "47", "/var/www/html/src/admin/hooks/useImageOptimization.js": "48", "/var/www/html/src/admin/components/common/AnimatedCard.jsx": "49", "/var/www/html/src/admin/components/common/LoadingSpinner.jsx": "50", "/var/www/html/src/admin/components/common/MobileNavigation.jsx": "51", "/var/www/html/src/admin/components/common/StatsCard.jsx": "52", "/var/www/html/src/admin/components/common/Toast.jsx": "53", "/var/www/html/src/admin/components/common/Modal.jsx": "54", "/var/www/html/src/admin/components/common/ModalTest.jsx": "55"}, {"size": 463, "mtime": 1752635557499, "results": "56", "hashOfConfig": "57"}, {"size": 1725, "mtime": 1752631575132, "results": "58", "hashOfConfig": "57"}, {"size": 3856, "mtime": 1752709117594, "results": "59", "hashOfConfig": "57"}, {"size": 1269, "mtime": 1752634460814, "results": "60", "hashOfConfig": "57"}, {"size": 477, "mtime": 1752634978610, "results": "61", "hashOfConfig": "57"}, {"size": 475, "mtime": 1752634943717, "results": "62", "hashOfConfig": "57"}, {"size": 645, "mtime": 1752634586689, "results": "63", "hashOfConfig": "57"}, {"size": 477, "mtime": 1752634960134, "results": "64", "hashOfConfig": "57"}, {"size": 482, "mtime": 1752634995310, "results": "65", "hashOfConfig": "57"}, {"size": 16812, "mtime": 1752645642196, "results": "66", "hashOfConfig": "57"}, {"size": 4578, "mtime": 1752704300372, "results": "67", "hashOfConfig": "57"}, {"size": 17055, "mtime": 1752645598689, "results": "68", "hashOfConfig": "57"}, {"size": 9254, "mtime": 1754351301375, "results": "69", "hashOfConfig": "57"}, {"size": 5952, "mtime": 1751443429426, "results": "70", "hashOfConfig": "57"}, {"size": 9709, "mtime": 1751561588128, "results": "71", "hashOfConfig": "57"}, {"size": 10324, "mtime": 1751443430306, "results": "72", "hashOfConfig": "57"}, {"size": 22132, "mtime": 1754352950641, "results": "73", "hashOfConfig": "57"}, {"size": 14343, "mtime": 1754351186568, "results": "74", "hashOfConfig": "57"}, {"size": 17788, "mtime": 1751601289625, "results": "75", "hashOfConfig": "57"}, {"size": 13939, "mtime": 1753919578260, "results": "76", "hashOfConfig": "57"}, {"size": 17095, "mtime": 1754352983383, "results": "77", "hashOfConfig": "57"}, {"size": 14750, "mtime": 1751443432969, "results": "78", "hashOfConfig": "57"}, {"size": 12975, "mtime": 1751601688079, "results": "79", "hashOfConfig": "57"}, {"size": 3828, "mtime": 1751478236235, "results": "80", "hashOfConfig": "57"}, {"size": 10376, "mtime": 1751606566385, "results": "81", "hashOfConfig": "57"}, {"size": 4902, "mtime": 1753919873520, "results": "82", "hashOfConfig": "57"}, {"size": 28308, "mtime": 1752710560401, "results": "83", "hashOfConfig": "57"}, {"size": 923, "mtime": 1751443427609, "results": "84", "hashOfConfig": "57"}, {"size": 6319, "mtime": 1752645029055, "results": "85", "hashOfConfig": "57"}, {"size": 4282, "mtime": 1752643007566, "results": "86", "hashOfConfig": "57"}, {"size": 31159, "mtime": 1752710311176, "results": "87", "hashOfConfig": "57"}, {"size": 16900, "mtime": 1752710588247, "results": "88", "hashOfConfig": "57"}, {"size": 5001, "mtime": 1752632019825, "results": "89", "hashOfConfig": "57"}, {"size": 3517, "mtime": 1751443429007, "results": "90", "hashOfConfig": "57"}, {"size": 9400, "mtime": 1751696106894, "results": "91", "hashOfConfig": "57"}, {"size": 1438, "mtime": 1753919688541, "results": "92", "hashOfConfig": "57"}, {"size": 5079, "mtime": 1752632642474, "results": "93", "hashOfConfig": "57"}, {"size": 7865, "mtime": 1752705598884, "results": "94", "hashOfConfig": "57"}, {"size": 3314, "mtime": 1752646795554, "results": "95", "hashOfConfig": "57"}, {"size": 8918, "mtime": 1752707917372, "results": "96", "hashOfConfig": "57"}, {"size": 8818, "mtime": 1752707064396, "results": "97", "hashOfConfig": "57"}, {"size": 2067, "mtime": 1752704615289, "results": "98", "hashOfConfig": "57"}, {"size": 12425, "mtime": 1752707390469, "results": "99", "hashOfConfig": "57"}, {"size": 8907, "mtime": 1752707892765, "results": "100", "hashOfConfig": "57"}, {"size": 4978, "mtime": 1752707633164, "results": "101", "hashOfConfig": "57"}, {"size": 11083, "mtime": 1752707311184, "results": "102", "hashOfConfig": "57"}, {"size": 3501, "mtime": 1752707672068, "results": "103", "hashOfConfig": "57"}, {"size": 6709, "mtime": 1753916439499, "results": "104", "hashOfConfig": "57"}, {"size": 2138, "mtime": 1754350936397, "results": "105", "hashOfConfig": "57"}, {"size": 1361, "mtime": 1754351324775, "results": "106", "hashOfConfig": "57"}, {"size": 4467, "mtime": 1754351258230, "results": "107", "hashOfConfig": "57"}, {"size": 3801, "mtime": 1754350976770, "results": "108", "hashOfConfig": "57"}, {"size": 3643, "mtime": 1754351351829, "results": "109", "hashOfConfig": "57"}, {"size": 2101, "mtime": 1754353536162, "results": "110", "hashOfConfig": "57"}, {"size": 1368, "mtime": 1754353017110, "results": "111", "hashOfConfig": "57"}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lomtko", {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/var/www/html/pages/_app.js", [], [], "/var/www/html/pages/_document.js", [], [], "/var/www/html/pages/admin.js", [], [], "/var/www/html/pages/api/[...slug].js", [], [], "/var/www/html/pages/api/cabinets.js", [], [], "/var/www/html/pages/api/footer.js", [], [], "/var/www/html/pages/api/hero.js", [], [], "/var/www/html/pages/api/kitchens.js", [], [], "/var/www/html/pages/api/why-choose-us.js", [], [], "/var/www/html/pages/cabinets.js", ["277", "278", "279", "280"], [], "/var/www/html/pages/index.js", [], [], "/var/www/html/pages/kitchens.js", ["281", "282", "283", "284"], [], "/var/www/html/src/admin/components/Dashboard.jsx", [], [], "/var/www/html/src/admin/components/Header.jsx", [], [], "/var/www/html/src/admin/components/Login.jsx", [], [], "/var/www/html/src/admin/components/Sidebar.jsx", [], [], "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx", ["285", "286", "287", "288"], [], "/var/www/html/src/admin/components/sections/DashboardHome.jsx", [], [], "/var/www/html/src/admin/components/sections/FooterManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/HeroManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/KitchensManagement.jsx", ["289", "290"], [], "/var/www/html/src/admin/components/sections/UserManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx", [], [], "/var/www/html/src/admin/context/AuthContext.jsx", [], [], "/var/www/html/src/admin/context/DataContext.jsx", [], [], "/var/www/html/src/admin/utils/initDatabase.js", ["291"], [], "/var/www/html/src/components/CabinetGallery.jsx", ["292", "293", "294", "295"], [], "/var/www/html/src/components/CallToAction.jsx", [], [], "/var/www/html/src/components/Footer.jsx", ["296"], [], "/var/www/html/src/components/HeroSection.jsx", ["297"], [], "/var/www/html/src/components/KitchenGallery.jsx", ["298", "299", "300", "301"], [], "/var/www/html/src/components/Navbar.jsx", [], [], "/var/www/html/src/components/SEO.jsx", [], [], "/var/www/html/src/components/Testimonials.jsx", [], [], "/var/www/html/src/components/WhyChooseUs.jsx", ["302"], [], "/var/www/html/src/config/api.js", [], [], "/var/www/html/src/config/env.js", [], [], "/var/www/html/src/components/mobile/MobileAbout.jsx", [], [], "/var/www/html/src/components/mobile/MobileBottomNav.jsx", [], [], "/var/www/html/src/components/mobile/MobileCabinets.jsx", [], [], "/var/www/html/src/components/mobile/MobileContact.jsx", [], [], "/var/www/html/src/components/mobile/MobileHeader.jsx", [], [], "/var/www/html/src/components/mobile/MobileHome.jsx", [], [], "/var/www/html/src/components/mobile/MobileKitchens.jsx", [], [], "/var/www/html/src/components/mobile/MobileLayout.jsx", [], [], "/var/www/html/src/components/mobile/MobileProductModal.jsx", [], [], "/var/www/html/src/utils/performance.js", [], [], "/var/www/html/src/admin/hooks/useImageOptimization.js", [], [], "/var/www/html/src/admin/components/common/AnimatedCard.jsx", [], [], "/var/www/html/src/admin/components/common/LoadingSpinner.jsx", [], [], "/var/www/html/src/admin/components/common/MobileNavigation.jsx", [], [], "/var/www/html/src/admin/components/common/StatsCard.jsx", [], [], "/var/www/html/src/admin/components/common/Toast.jsx", [], [], "/var/www/html/src/admin/components/common/Modal.jsx", [], [], "/var/www/html/src/admin/components/common/ModalTest.jsx", [], [], {"ruleId": "303", "severity": 1, "message": "304", "line": 75, "column": 6, "nodeType": "305", "endLine": 75, "endColumn": 8, "suggestions": "306"}, {"ruleId": "307", "severity": 1, "message": "308", "line": 127, "column": 21, "nodeType": "309", "endLine": 131, "endColumn": 23}, {"ruleId": "307", "severity": 1, "message": "308", "line": 178, "column": 29, "nodeType": "309", "endLine": 182, "endColumn": 31}, {"ruleId": "307", "severity": 1, "message": "308", "line": 199, "column": 31, "nodeType": "309", "endLine": 203, "endColumn": 33}, {"ruleId": "303", "severity": 1, "message": "310", "line": 78, "column": 6, "nodeType": "305", "endLine": 78, "endColumn": 8, "suggestions": "311"}, {"ruleId": "307", "severity": 1, "message": "308", "line": 130, "column": 21, "nodeType": "309", "endLine": 134, "endColumn": 23}, {"ruleId": "307", "severity": 1, "message": "308", "line": 181, "column": 29, "nodeType": "309", "endLine": 185, "endColumn": 31}, {"ruleId": "307", "severity": 1, "message": "308", "line": 202, "column": 31, "nodeType": "309", "endLine": 206, "endColumn": 33}, {"ruleId": "303", "severity": 1, "message": "312", "line": 45, "column": 27, "nodeType": "313", "endLine": 45, "endColumn": 38}, {"ruleId": "303", "severity": 1, "message": "314", "line": 61, "column": 6, "nodeType": "305", "endLine": 61, "endColumn": 32, "suggestions": "315"}, {"ruleId": "307", "severity": 1, "message": "308", "line": 342, "column": 15, "nodeType": "309", "endLine": 346, "endColumn": 17}, {"ruleId": "307", "severity": 1, "message": "308", "line": 375, "column": 23, "nodeType": "309", "endLine": 375, "endColumn": 101}, {"ruleId": "307", "severity": 1, "message": "308", "line": 274, "column": 15, "nodeType": "309", "endLine": 278, "endColumn": 17}, {"ruleId": "307", "severity": 1, "message": "308", "line": 307, "column": 23, "nodeType": "309", "endLine": 307, "endColumn": 101}, {"ruleId": "316", "severity": 1, "message": "317", "line": 154, "column": 1, "nodeType": "318", "endLine": 160, "endColumn": 2}, {"ruleId": "303", "severity": 1, "message": "319", "line": 104, "column": 6, "nodeType": "305", "endLine": 104, "endColumn": 8, "suggestions": "320"}, {"ruleId": "307", "severity": 1, "message": "308", "line": 287, "column": 23, "nodeType": "309", "endLine": 291, "endColumn": 25}, {"ruleId": "307", "severity": 1, "message": "308", "line": 500, "column": 27, "nodeType": "309", "endLine": 504, "endColumn": 29}, {"ruleId": "307", "severity": 1, "message": "308", "line": 515, "column": 19, "nodeType": "309", "endLine": 519, "endColumn": 21}, {"ruleId": "303", "severity": 1, "message": "321", "line": 58, "column": 6, "nodeType": "305", "endLine": 58, "endColumn": 8, "suggestions": "322"}, {"ruleId": "303", "severity": 1, "message": "323", "line": 47, "column": 6, "nodeType": "305", "endLine": 47, "endColumn": 8, "suggestions": "324"}, {"ruleId": "303", "severity": 1, "message": "325", "line": 106, "column": 6, "nodeType": "305", "endLine": 106, "endColumn": 8, "suggestions": "326"}, {"ruleId": "307", "severity": 1, "message": "308", "line": 408, "column": 19, "nodeType": "309", "endLine": 412, "endColumn": 21}, {"ruleId": "307", "severity": 1, "message": "308", "line": 577, "column": 27, "nodeType": "309", "endLine": 581, "endColumn": 29}, {"ruleId": "307", "severity": 1, "message": "308", "line": 592, "column": 19, "nodeType": "309", "endLine": 596, "endColumn": 21}, {"ruleId": "303", "severity": 1, "message": "327", "line": 99, "column": 6, "nodeType": "305", "endLine": 99, "endColumn": 8, "suggestions": "328"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fallbackCabinets'. Either include it or remove the dependency array.", "ArrayExpression", ["329"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fallbackKitchens'. Either include it or remove the dependency array.", ["330"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "Identifier", "React Hook useMemo has a missing dependency: 'getCategoryName'. Either include it or remove the dependency array.", ["331"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'defaultCabinets'. Either include it or remove the dependency array.", ["332"], "React Hook useEffect has a missing dependency: 'defaultFooterData'. Either include it or remove the dependency array.", ["333"], "React Hook useEffect has a missing dependency: 'defaultHeroData'. Either include it or remove the dependency array.", ["334"], "React Hook useEffect has a missing dependency: 'defaultKitchens'. Either include it or remove the dependency array.", ["335"], "React Hook useEffect has a missing dependency: 'defaultWhyChooseUsData'. Either include it or remove the dependency array.", ["336"], {"desc": "337", "fix": "338"}, {"desc": "339", "fix": "340"}, {"desc": "341", "fix": "342"}, {"desc": "343", "fix": "344"}, {"desc": "345", "fix": "346"}, {"desc": "347", "fix": "348"}, {"desc": "349", "fix": "350"}, {"desc": "351", "fix": "352"}, "Update the dependencies array to be: [fallbackCabinets]", {"range": "353", "text": "354"}, "Update the dependencies array to be: [fallback<PERSON><PERSON><PERSON>]", {"range": "355", "text": "356"}, "Update the dependencies array to be: [cabinetsData, getCategoryName, searchTerm]", {"range": "357", "text": "358"}, "Update the dependencies array to be: [defaultCabinets]", {"range": "359", "text": "360"}, "Update the dependencies array to be: [defaultFooterData]", {"range": "361", "text": "362"}, "Update the dependencies array to be: [defaultHeroData]", {"range": "363", "text": "364"}, "Update the dependencies array to be: [default<PERSON><PERSON>ens]", {"range": "365", "text": "366"}, "Update the dependencies array to be: [defaultWhyChooseUsData]", {"range": "367", "text": "368"}, [2924, 2926], "[fallbackCabinets]", [3099, 3101], "[fallback<PERSON><PERSON><PERSON>]", [2071, 2097], "[cabinetsData, getCategoryName, searchTerm]", [3820, 3822], "[defaultCabinets]", [2493, 2495], "[defaultFooterData]", [1825, 1827], "[defaultHeroData]", [3944, 3946], "[default<PERSON><PERSON><PERSON>]", [3175, 3177], "[defaultWhyChooseUsData]"]