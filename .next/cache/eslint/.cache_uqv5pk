[{"/var/www/html/pages/_app.js": "1", "/var/www/html/pages/_document.js": "2", "/var/www/html/pages/admin.js": "3", "/var/www/html/pages/api/[...slug].js": "4", "/var/www/html/pages/api/cabinets.js": "5", "/var/www/html/pages/api/footer.js": "6", "/var/www/html/pages/api/hero.js": "7", "/var/www/html/pages/api/kitchens.js": "8", "/var/www/html/pages/api/why-choose-us.js": "9", "/var/www/html/pages/cabinets.js": "10", "/var/www/html/pages/index.js": "11", "/var/www/html/pages/kitchens.js": "12", "/var/www/html/src/admin/components/Dashboard.jsx": "13", "/var/www/html/src/admin/components/Header.jsx": "14", "/var/www/html/src/admin/components/Login.jsx": "15", "/var/www/html/src/admin/components/Sidebar.jsx": "16", "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx": "17", "/var/www/html/src/admin/components/sections/DashboardHome.jsx": "18", "/var/www/html/src/admin/components/sections/FooterManagement.jsx": "19", "/var/www/html/src/admin/components/sections/HeroManagement.jsx": "20", "/var/www/html/src/admin/components/sections/KitchensManagement.jsx": "21", "/var/www/html/src/admin/components/sections/UserManagement.jsx": "22", "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx": "23", "/var/www/html/src/admin/context/AuthContext.jsx": "24", "/var/www/html/src/admin/context/DataContext.jsx": "25", "/var/www/html/src/admin/utils/initDatabase.js": "26", "/var/www/html/src/components/CabinetGallery.jsx": "27", "/var/www/html/src/components/CallToAction.jsx": "28", "/var/www/html/src/components/Footer.jsx": "29", "/var/www/html/src/components/HeroSection.jsx": "30", "/var/www/html/src/components/KitchenGallery.jsx": "31", "/var/www/html/src/components/Navbar.jsx": "32", "/var/www/html/src/components/SEO.jsx": "33", "/var/www/html/src/components/Testimonials.jsx": "34", "/var/www/html/src/components/WhyChooseUs.jsx": "35", "/var/www/html/src/config/api.js": "36", "/var/www/html/src/config/env.js": "37", "/var/www/html/src/components/mobile/MobileAbout.jsx": "38", "/var/www/html/src/components/mobile/MobileBottomNav.jsx": "39", "/var/www/html/src/components/mobile/MobileCabinets.jsx": "40", "/var/www/html/src/components/mobile/MobileContact.jsx": "41", "/var/www/html/src/components/mobile/MobileHeader.jsx": "42", "/var/www/html/src/components/mobile/MobileHome.jsx": "43", "/var/www/html/src/components/mobile/MobileKitchens.jsx": "44", "/var/www/html/src/components/mobile/MobileLayout.jsx": "45", "/var/www/html/src/components/mobile/MobileProductModal.jsx": "46", "/var/www/html/src/utils/performance.js": "47", "/var/www/html/src/admin/hooks/useImageOptimization.js": "48", "/var/www/html/src/admin/components/common/AnimatedCard.jsx": "49", "/var/www/html/src/admin/components/common/LoadingSpinner.jsx": "50", "/var/www/html/src/admin/components/common/MobileNavigation.jsx": "51", "/var/www/html/src/admin/components/common/StatsCard.jsx": "52", "/var/www/html/src/admin/components/common/Toast.jsx": "53"}, {"size": 463, "mtime": 1752635557499, "results": "54", "hashOfConfig": "55"}, {"size": 1725, "mtime": 1752631575132, "results": "56", "hashOfConfig": "55"}, {"size": 3856, "mtime": 1752709117594, "results": "57", "hashOfConfig": "55"}, {"size": 1269, "mtime": 1752634460814, "results": "58", "hashOfConfig": "55"}, {"size": 477, "mtime": 1752634978610, "results": "59", "hashOfConfig": "55"}, {"size": 475, "mtime": 1752634943717, "results": "60", "hashOfConfig": "55"}, {"size": 645, "mtime": 1752634586689, "results": "61", "hashOfConfig": "55"}, {"size": 477, "mtime": 1752634960134, "results": "62", "hashOfConfig": "55"}, {"size": 482, "mtime": 1752634995310, "results": "63", "hashOfConfig": "55"}, {"size": 16812, "mtime": 1752645642196, "results": "64", "hashOfConfig": "55"}, {"size": 4578, "mtime": 1752704300372, "results": "65", "hashOfConfig": "55"}, {"size": 17055, "mtime": 1752645598689, "results": "66", "hashOfConfig": "55"}, {"size": 9254, "mtime": 1754351301375, "results": "67", "hashOfConfig": "55"}, {"size": 5952, "mtime": 1751443429426, "results": "68", "hashOfConfig": "55"}, {"size": 9709, "mtime": 1751561588128, "results": "69", "hashOfConfig": "55"}, {"size": 10324, "mtime": 1751443430306, "results": "70", "hashOfConfig": "55"}, {"size": 24008, "mtime": 1753919555648, "results": "71", "hashOfConfig": "55"}, {"size": 14343, "mtime": 1754351186568, "results": "72", "hashOfConfig": "55"}, {"size": 17788, "mtime": 1751601289625, "results": "73", "hashOfConfig": "55"}, {"size": 13939, "mtime": 1753919578260, "results": "74", "hashOfConfig": "55"}, {"size": 19104, "mtime": 1753919530530, "results": "75", "hashOfConfig": "55"}, {"size": 14750, "mtime": 1751443432969, "results": "76", "hashOfConfig": "55"}, {"size": 12975, "mtime": 1751601688079, "results": "77", "hashOfConfig": "55"}, {"size": 3828, "mtime": 1751478236235, "results": "78", "hashOfConfig": "55"}, {"size": 10376, "mtime": 1751606566385, "results": "79", "hashOfConfig": "55"}, {"size": 4902, "mtime": 1753919873520, "results": "80", "hashOfConfig": "55"}, {"size": 28308, "mtime": 1752710560401, "results": "81", "hashOfConfig": "55"}, {"size": 923, "mtime": 1751443427609, "results": "82", "hashOfConfig": "55"}, {"size": 6319, "mtime": 1752645029055, "results": "83", "hashOfConfig": "55"}, {"size": 4282, "mtime": 1752643007566, "results": "84", "hashOfConfig": "55"}, {"size": 31159, "mtime": 1752710311176, "results": "85", "hashOfConfig": "55"}, {"size": 16900, "mtime": 1752710588247, "results": "86", "hashOfConfig": "55"}, {"size": 5001, "mtime": 1752632019825, "results": "87", "hashOfConfig": "55"}, {"size": 3517, "mtime": 1751443429007, "results": "88", "hashOfConfig": "55"}, {"size": 9400, "mtime": 1751696106894, "results": "89", "hashOfConfig": "55"}, {"size": 1438, "mtime": 1753919688541, "results": "90", "hashOfConfig": "55"}, {"size": 5079, "mtime": 1752632642474, "results": "91", "hashOfConfig": "55"}, {"size": 7865, "mtime": 1752705598884, "results": "92", "hashOfConfig": "55"}, {"size": 3314, "mtime": 1752646795554, "results": "93", "hashOfConfig": "55"}, {"size": 8918, "mtime": 1752707917372, "results": "94", "hashOfConfig": "55"}, {"size": 8818, "mtime": 1752707064396, "results": "95", "hashOfConfig": "55"}, {"size": 2067, "mtime": 1752704615289, "results": "96", "hashOfConfig": "55"}, {"size": 12425, "mtime": 1752707390469, "results": "97", "hashOfConfig": "55"}, {"size": 8907, "mtime": 1752707892765, "results": "98", "hashOfConfig": "55"}, {"size": 4978, "mtime": 1752707633164, "results": "99", "hashOfConfig": "55"}, {"size": 11083, "mtime": 1752707311184, "results": "100", "hashOfConfig": "55"}, {"size": 3501, "mtime": 1752707672068, "results": "101", "hashOfConfig": "55"}, {"size": 6709, "mtime": 1753916439499, "results": "102", "hashOfConfig": "55"}, {"size": 2138, "mtime": 1754350936397, "results": "103", "hashOfConfig": "55"}, {"size": 1361, "mtime": 1754351324775, "results": "104", "hashOfConfig": "55"}, {"size": 4467, "mtime": 1754351258230, "results": "105", "hashOfConfig": "55"}, {"size": 3801, "mtime": 1754350976770, "results": "106", "hashOfConfig": "55"}, {"size": 3643, "mtime": 1754351351829, "results": "107", "hashOfConfig": "55"}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lomtko", {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/var/www/html/pages/_app.js", [], [], "/var/www/html/pages/_document.js", [], [], "/var/www/html/pages/admin.js", [], [], "/var/www/html/pages/api/[...slug].js", [], [], "/var/www/html/pages/api/cabinets.js", [], [], "/var/www/html/pages/api/footer.js", [], [], "/var/www/html/pages/api/hero.js", [], [], "/var/www/html/pages/api/kitchens.js", [], [], "/var/www/html/pages/api/why-choose-us.js", [], [], "/var/www/html/pages/cabinets.js", ["267", "268", "269", "270"], [], "/var/www/html/pages/index.js", [], [], "/var/www/html/pages/kitchens.js", ["271", "272", "273", "274"], [], "/var/www/html/src/admin/components/Dashboard.jsx", [], [], "/var/www/html/src/admin/components/Header.jsx", [], [], "/var/www/html/src/admin/components/Login.jsx", [], [], "/var/www/html/src/admin/components/Sidebar.jsx", [], [], "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx", ["275", "276", "277", "278"], [], "/var/www/html/src/admin/components/sections/DashboardHome.jsx", [], [], "/var/www/html/src/admin/components/sections/FooterManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/HeroManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/KitchensManagement.jsx", ["279", "280"], [], "/var/www/html/src/admin/components/sections/UserManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx", [], [], "/var/www/html/src/admin/context/AuthContext.jsx", [], [], "/var/www/html/src/admin/context/DataContext.jsx", [], [], "/var/www/html/src/admin/utils/initDatabase.js", ["281"], [], "/var/www/html/src/components/CabinetGallery.jsx", ["282", "283", "284", "285"], [], "/var/www/html/src/components/CallToAction.jsx", [], [], "/var/www/html/src/components/Footer.jsx", ["286"], [], "/var/www/html/src/components/HeroSection.jsx", ["287"], [], "/var/www/html/src/components/KitchenGallery.jsx", ["288", "289", "290", "291"], [], "/var/www/html/src/components/Navbar.jsx", [], [], "/var/www/html/src/components/SEO.jsx", [], [], "/var/www/html/src/components/Testimonials.jsx", [], [], "/var/www/html/src/components/WhyChooseUs.jsx", ["292"], [], "/var/www/html/src/config/api.js", [], [], "/var/www/html/src/config/env.js", [], [], "/var/www/html/src/components/mobile/MobileAbout.jsx", [], [], "/var/www/html/src/components/mobile/MobileBottomNav.jsx", [], [], "/var/www/html/src/components/mobile/MobileCabinets.jsx", [], [], "/var/www/html/src/components/mobile/MobileContact.jsx", [], [], "/var/www/html/src/components/mobile/MobileHeader.jsx", [], [], "/var/www/html/src/components/mobile/MobileHome.jsx", [], [], "/var/www/html/src/components/mobile/MobileKitchens.jsx", [], [], "/var/www/html/src/components/mobile/MobileLayout.jsx", [], [], "/var/www/html/src/components/mobile/MobileProductModal.jsx", [], [], "/var/www/html/src/utils/performance.js", [], [], "/var/www/html/src/admin/hooks/useImageOptimization.js", [], [], "/var/www/html/src/admin/components/common/AnimatedCard.jsx", [], [], "/var/www/html/src/admin/components/common/LoadingSpinner.jsx", [], [], "/var/www/html/src/admin/components/common/MobileNavigation.jsx", [], [], "/var/www/html/src/admin/components/common/StatsCard.jsx", [], [], "/var/www/html/src/admin/components/common/Toast.jsx", [], [], {"ruleId": "293", "severity": 1, "message": "294", "line": 75, "column": 6, "nodeType": "295", "endLine": 75, "endColumn": 8, "suggestions": "296"}, {"ruleId": "297", "severity": 1, "message": "298", "line": 127, "column": 21, "nodeType": "299", "endLine": 131, "endColumn": 23}, {"ruleId": "297", "severity": 1, "message": "298", "line": 178, "column": 29, "nodeType": "299", "endLine": 182, "endColumn": 31}, {"ruleId": "297", "severity": 1, "message": "298", "line": 199, "column": 31, "nodeType": "299", "endLine": 203, "endColumn": 33}, {"ruleId": "293", "severity": 1, "message": "300", "line": 78, "column": 6, "nodeType": "295", "endLine": 78, "endColumn": 8, "suggestions": "301"}, {"ruleId": "297", "severity": 1, "message": "298", "line": 130, "column": 21, "nodeType": "299", "endLine": 134, "endColumn": 23}, {"ruleId": "297", "severity": 1, "message": "298", "line": 181, "column": 29, "nodeType": "299", "endLine": 185, "endColumn": 31}, {"ruleId": "297", "severity": 1, "message": "298", "line": 202, "column": 31, "nodeType": "299", "endLine": 206, "endColumn": 33}, {"ruleId": "293", "severity": 1, "message": "302", "line": 44, "column": 27, "nodeType": "303", "endLine": 44, "endColumn": 38}, {"ruleId": "293", "severity": 1, "message": "304", "line": 60, "column": 6, "nodeType": "295", "endLine": 60, "endColumn": 32, "suggestions": "305"}, {"ruleId": "297", "severity": 1, "message": "298", "line": 358, "column": 15, "nodeType": "299", "endLine": 362, "endColumn": 17}, {"ruleId": "297", "severity": 1, "message": "298", "line": 391, "column": 23, "nodeType": "299", "endLine": 391, "endColumn": 101}, {"ruleId": "297", "severity": 1, "message": "298", "line": 293, "column": 15, "nodeType": "299", "endLine": 297, "endColumn": 17}, {"ruleId": "297", "severity": 1, "message": "298", "line": 326, "column": 23, "nodeType": "299", "endLine": 326, "endColumn": 101}, {"ruleId": "306", "severity": 1, "message": "307", "line": 154, "column": 1, "nodeType": "308", "endLine": 160, "endColumn": 2}, {"ruleId": "293", "severity": 1, "message": "309", "line": 104, "column": 6, "nodeType": "295", "endLine": 104, "endColumn": 8, "suggestions": "310"}, {"ruleId": "297", "severity": 1, "message": "298", "line": 287, "column": 23, "nodeType": "299", "endLine": 291, "endColumn": 25}, {"ruleId": "297", "severity": 1, "message": "298", "line": 500, "column": 27, "nodeType": "299", "endLine": 504, "endColumn": 29}, {"ruleId": "297", "severity": 1, "message": "298", "line": 515, "column": 19, "nodeType": "299", "endLine": 519, "endColumn": 21}, {"ruleId": "293", "severity": 1, "message": "311", "line": 58, "column": 6, "nodeType": "295", "endLine": 58, "endColumn": 8, "suggestions": "312"}, {"ruleId": "293", "severity": 1, "message": "313", "line": 47, "column": 6, "nodeType": "295", "endLine": 47, "endColumn": 8, "suggestions": "314"}, {"ruleId": "293", "severity": 1, "message": "315", "line": 106, "column": 6, "nodeType": "295", "endLine": 106, "endColumn": 8, "suggestions": "316"}, {"ruleId": "297", "severity": 1, "message": "298", "line": 408, "column": 19, "nodeType": "299", "endLine": 412, "endColumn": 21}, {"ruleId": "297", "severity": 1, "message": "298", "line": 577, "column": 27, "nodeType": "299", "endLine": 581, "endColumn": 29}, {"ruleId": "297", "severity": 1, "message": "298", "line": 592, "column": 19, "nodeType": "299", "endLine": 596, "endColumn": 21}, {"ruleId": "293", "severity": 1, "message": "317", "line": 99, "column": 6, "nodeType": "295", "endLine": 99, "endColumn": 8, "suggestions": "318"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fallbackCabinets'. Either include it or remove the dependency array.", "ArrayExpression", ["319"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fallbackKitchens'. Either include it or remove the dependency array.", ["320"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "Identifier", "React Hook useMemo has a missing dependency: 'getCategoryName'. Either include it or remove the dependency array.", ["321"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'defaultCabinets'. Either include it or remove the dependency array.", ["322"], "React Hook useEffect has a missing dependency: 'defaultFooterData'. Either include it or remove the dependency array.", ["323"], "React Hook useEffect has a missing dependency: 'defaultHeroData'. Either include it or remove the dependency array.", ["324"], "React Hook useEffect has a missing dependency: 'defaultKitchens'. Either include it or remove the dependency array.", ["325"], "React Hook useEffect has a missing dependency: 'defaultWhyChooseUsData'. Either include it or remove the dependency array.", ["326"], {"desc": "327", "fix": "328"}, {"desc": "329", "fix": "330"}, {"desc": "331", "fix": "332"}, {"desc": "333", "fix": "334"}, {"desc": "335", "fix": "336"}, {"desc": "337", "fix": "338"}, {"desc": "339", "fix": "340"}, {"desc": "341", "fix": "342"}, "Update the dependencies array to be: [fallbackCabinets]", {"range": "343", "text": "344"}, "Update the dependencies array to be: [fallback<PERSON><PERSON><PERSON>]", {"range": "345", "text": "346"}, "Update the dependencies array to be: [cabinetsData, getCategoryName, searchTerm]", {"range": "347", "text": "348"}, "Update the dependencies array to be: [defaultCabinets]", {"range": "349", "text": "350"}, "Update the dependencies array to be: [defaultFooterData]", {"range": "351", "text": "352"}, "Update the dependencies array to be: [defaultHeroData]", {"range": "353", "text": "354"}, "Update the dependencies array to be: [default<PERSON><PERSON>ens]", {"range": "355", "text": "356"}, "Update the dependencies array to be: [defaultWhyChooseUsData]", {"range": "357", "text": "358"}, [2924, 2926], "[fallbackCabinets]", [3099, 3101], "[fallback<PERSON><PERSON><PERSON>]", [2035, 2061], "[cabinetsData, getCategoryName, searchTerm]", [3820, 3822], "[defaultCabinets]", [2493, 2495], "[defaultFooterData]", [1825, 1827], "[defaultHeroData]", [3944, 3946], "[default<PERSON><PERSON><PERSON>]", [3175, 3177], "[defaultWhyChooseUsData]"]