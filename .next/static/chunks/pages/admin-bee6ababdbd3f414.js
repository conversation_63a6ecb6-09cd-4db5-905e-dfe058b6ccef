(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[309],{4805:(e,t,s)=>{"use strict";s.d(t,{sg:()=>a,yk:()=>l});let a=(e,t)=>{let s;return function(){for(var a=arguments.length,l=Array(a),r=0;r<a;r++)l[r]=arguments[r];clearTimeout(s),s=setTimeout(()=>{clearTimeout(s),e(...l)},t)}},l={set:function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5,a={data:t,timestamp:Date.now(),ttl:s};localStorage.setItem(e,JSON.stringify(a))},get:e=>{try{let t=JSON.parse(localStorage.getItem(e));if(!t)return null;if(Date.now()-t.timestamp>t.ttl)return localStorage.removeItem(e),null;return t.data}catch(e){return null}},clear:e=>{localStorage.removeItem(e)},clearAll:()=>{localStorage.clear()}}},6221:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>X});var a=s(7876),l=s(4232),r=s(7328),i=s.n(r),n=s(5364);let c=e=>{try{return atob(e)}catch(e){return""}},d=e=>{try{return btoa(e)}catch(e){return""}},o={name:n.env.NEXT_PUBLIC_APP_NAME||"عجائب الخبراء",domain:n.env.NEXT_PUBLIC_APP_DOMAIN||"khobrakitchens.com",url:n.env.NEXT_PUBLIC_APP_URL||"https://khobrakitchens.com",serverIP:n.env.NEXT_PUBLIC_SERVER_IP||"************",isProduction:!0,debug:"true"===n.env.NEXT_PUBLIC_DEBUG},m={username:c(n.env.NEXT_PUBLIC_ADMIN_USERNAME_HASH||d("admin_khobra_kitchens")),email:n.env.NEXT_PUBLIC_ADMIN_EMAIL||"<EMAIL>",password:c(n.env.NEXT_PUBLIC_ADMIN_PASSWORD_HASH||d("khobra_admin_2024")),sessionTimeout:parseInt(n.env.NEXT_PUBLIC_SESSION_TIMEOUT||"3600000")},x={jwtSecret:n.env.NEXT_PUBLIC_JWT_SECRET||"khobra_kitchens_jwt_secret_2024_secure",encryptionKey:n.env.NEXT_PUBLIC_ENCRYPTION_KEY||"khobra_enc_key_2024",sessionTimeout:parseInt(n.env.NEXT_PUBLIC_SESSION_TIMEOUT||"3600000")},u={title:n.env.NEXT_PUBLIC_SITE_TITLE||"عجائب الخبراء - تصميم وتنفيذ المطابخ والخزائن",description:n.env.NEXT_PUBLIC_SITE_DESCRIPTION||"شركة عجائب الخبراء المتخصصة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية بأعلى معايير الجودة في المملكة العربية السعودية",keywords:n.env.NEXT_PUBLIC_SITE_KEYWORDS||"مطابخ,خزائن,تصميم مطابخ,مطابخ عصرية,مطابخ كلاسيكية,خزائن ملابس,تفصيل مطابخ,السعودية,عجائب الخبراء",author:n.env.NEXT_PUBLIC_SITE_AUTHOR||"عجائب الخبراء",ogImage:"".concat(o.url,"/images/og-image.jpg"),twitterCard:"summary_large_image"},h={name:n.env.NEXT_PUBLIC_COMPANY_NAME||"عجائب الخبراء",phone:n.env.NEXT_PUBLIC_COMPANY_PHONE||"0557611105",email:n.env.NEXT_PUBLIC_COMPANY_EMAIL||"<EMAIL>",address:n.env.NEXT_PUBLIC_COMPANY_ADDRESS||"الرياض، المملكة العربية السعودية"},g={twitter:n.env.NEXT_PUBLIC_TWITTER_URL||"https://twitter.com/khobrakitchens",instagram:n.env.NEXT_PUBLIC_INSTAGRAM_URL||"https://instagram.com/khobrakitchens",whatsapp:n.env.NEXT_PUBLIC_WHATSAPP_URL||"https://wa.me/966123456789",snapchat:n.env.NEXT_PUBLIC_SNAPCHAT_URL||"https://snapchat.com/add/khobrakitchens",tiktok:n.env.NEXT_PUBLIC_TIKTOK_URL||"https://tiktok.com/@khobrakitchens"};n.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,n.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID,o.isProduction&&(()=>{let e=[];return o.domain||e.push("Missing APP_DOMAIN"),m.username||e.push("Missing ADMIN_USERNAME"),m.password||e.push("Missing ADMIN_PASSWORD"),u.title||e.push("Missing SITE_TITLE"),!e.length})();let b=(0,l.createContext)(),p=()=>{let e=(0,l.useContext)(b);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},j=e=>{let{children:t}=e,[s,r]=(0,l.useState)(!1),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(null),o={username:"admin",email:"<EMAIL>",password:"admin123"};(0,l.useEffect)(()=>{localStorage.removeItem("admin_user_data");let e=localStorage.getItem("admin_token"),t=localStorage.getItem("admin_user"),s=localStorage.getItem("admin_login_time");e&&t&&s&&(Date.now()-parseInt(s)<x.sessionTimeout?(r(!0),d(JSON.parse(t))):m()),n(!1)},[]);let m=()=>{localStorage.removeItem("admin_token"),localStorage.removeItem("admin_user"),localStorage.removeItem("admin_login_time"),r(!1),d(null)};return(0,a.jsx)(b.Provider,{value:{isAuthenticated:s,user:c,loading:i,login:e=>new Promise((t,s)=>{setTimeout(()=>{if((e.username===o.username||e.email===o.email)&&e.password===o.password){let e="khobra_admin_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),s=Date.now().toString(),a={username:o.username,email:o.email,loginTime:s};localStorage.setItem("admin_token",e),localStorage.setItem("admin_user",JSON.stringify(a)),localStorage.setItem("admin_login_time",s),r(!0),d(a),t(a)}else s(Error("بيانات الدخول غير صحيحة"))},1e3)}),logout:m,updateUser:e=>{let t={...JSON.parse(localStorage.getItem("admin_user_data")||JSON.stringify(o)),...e};if(localStorage.setItem("admin_user_data",JSON.stringify(t)),e.username||e.email){let t={username:e.username||c.username,email:e.email||c.email};localStorage.setItem("admin_user",JSON.stringify(t)),d(t)}}},children:t})},f=()=>{let[e,t]=(0,l.useState)({username:"",password:""}),[s,r]=(0,l.useState)(!1),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)(!1),{login:o}=p(),m=s=>{t({...e,[s.target.name]:s.target.value}),n("")},x=async t=>{t.preventDefault(),r(!0),n("");try{await o(e)}catch(e){n(e.message)}finally{r(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 via-purple-50 to-pink-50"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\\'80\\' height=\\'80\\' viewBox=\\'0 0 80 80\\' xmlns=\\'http://www.w3.org/2000/svg\\'%3E%3Cg fill=\\'none\\' fill-rule=\\'evenodd\\'%3E%3Cg fill=\\'%239C92AC\\' fill-opacity=\\'0.04\\'%3E%3Ccircle cx=\\'40\\' cy=\\'40\\' r=\\'6\\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"}),(0,a.jsx)("div",{className:"absolute top-20 left-20 w-40 h-40 bg-gradient-to-br from-blue-400/15 via-purple-400/15 to-indigo-400/15 rounded-full blur-3xl animate-pulse float-animation"}),(0,a.jsx)("div",{className:"absolute bottom-20 right-20 w-48 h-48 bg-gradient-to-br from-purple-400/15 via-pink-400/15 to-rose-400/15 rounded-full blur-3xl animate-pulse delay-1000 float-animation"}),(0,a.jsx)("div",{className:"absolute top-1/2 left-10 w-32 h-32 bg-gradient-to-br from-indigo-400/15 via-blue-400/15 to-cyan-400/15 rounded-full blur-3xl animate-pulse delay-500 float-animation"}),(0,a.jsx)("div",{className:"absolute top-10 right-1/3 w-24 h-24 bg-gradient-to-br from-cyan-400/10 via-blue-400/10 to-indigo-400/10 rounded-full blur-2xl animate-pulse delay-700 float-animation"}),(0,a.jsx)("div",{className:"absolute bottom-1/3 left-1/4 w-36 h-36 bg-gradient-to-br from-rose-400/10 via-pink-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse delay-300 float-animation"}),(0,a.jsxs)("div",{className:"max-w-lg w-full mx-4 relative z-10",children:[(0,a.jsxs)("div",{className:"text-center mb-12 fade-in",children:[(0,a.jsxs)("div",{className:"relative inline-flex items-center justify-center w-32 h-32 mb-10 group",children:[(0,a.jsx)("div",{className:"w-32 h-32 bg-gradient-to-br from-blue-500 via-purple-600 via-indigo-600 to-blue-700 rounded-full flex items-center justify-center shadow-2xl shadow-blue-500/30 transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-6 pulse-glow",children:(0,a.jsx)("i",{className:"ri-admin-line text-5xl text-white"})}),(0,a.jsx)("div",{className:"absolute -inset-2 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-600 rounded-full blur-xl opacity-40 animate-pulse"}),(0,a.jsx)("div",{className:"absolute -inset-4 bg-gradient-to-br from-blue-400 via-purple-500 to-indigo-500 rounded-full blur-2xl opacity-20 animate-pulse delay-500"})]}),(0,a.jsx)("h1",{className:"text-5xl font-black bg-gradient-to-r from-gray-900 via-blue-800 via-purple-800 to-indigo-900 bg-clip-text text-transparent mb-4 tracking-tight",children:"لوحة التحكم"}),(0,a.jsx)("p",{className:"text-gray-700 text-xl font-bold mb-6",children:"عجائب الخبراء - إدارة المحتوى"}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-8 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"}),(0,a.jsx)("div",{className:"w-4 h-1 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full"}),(0,a.jsx)("div",{className:"w-12 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"})]})]}),(0,a.jsxs)("div",{className:"glass-card p-12 scale-in backdrop-blur-2xl bg-white/80 border border-white/30 shadow-3xl relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/50 via-blue-50/30 to-purple-50/30 rounded-3xl"}),(0,a.jsxs)("form",{onSubmit:x,className:"space-y-8 relative z-10",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{htmlFor:"username",className:"block text-lg font-black text-gray-800 mb-4",children:"اسم المستخدم أو البريد الإلكتروني"}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("input",{type:"text",id:"username",name:"username",value:e.username,onChange:m,className:"form-input pl-16 h-16 text-lg font-medium",placeholder:"",required:!0}),(0,a.jsx)("div",{className:"absolute left-5 top-1/2 transform -translate-y-1/2 transition-all duration-300 group-focus-within:text-blue-500 group-focus-within:scale-110",children:(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center group-focus-within:from-blue-500 group-focus-within:to-purple-500 transition-all duration-300",children:(0,a.jsx)("i",{className:"ri-user-line text-xl text-gray-500 group-focus-within:text-white"})})})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-lg font-black text-gray-800 mb-4",children:"كلمة المرور"}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("input",{type:c?"text":"password",id:"password",name:"password",value:e.password,onChange:m,className:"form-input pl-16 pr-16 h-16 text-lg font-medium",placeholder:"",required:!0}),(0,a.jsx)("div",{className:"absolute left-5 top-1/2 transform -translate-y-1/2 transition-all duration-300 group-focus-within:text-blue-500 group-focus-within:scale-110",children:(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center group-focus-within:from-blue-500 group-focus-within:to-purple-500 transition-all duration-300",children:(0,a.jsx)("i",{className:"ri-lock-line text-xl text-gray-500 group-focus-within:text-white"})})}),(0,a.jsx)("button",{type:"button",onClick:()=>d(!c),className:"absolute right-5 top-1/2 transform -translate-y-1/2 p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 hover:scale-110",children:(0,a.jsx)("i",{className:"".concat(c?"ri-eye-off-line":"ri-eye-line"," text-xl text-gray-500 hover:text-blue-500")})})]})]}),i&&(0,a.jsxs)("div",{className:"bg-gradient-to-r from-red-50 to-rose-50 border border-red-200/50 rounded-2xl p-6 flex items-center space-x-4 rtl:space-x-reverse animate-pulse shadow-lg",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-red-500 to-rose-600 rounded-2xl flex items-center justify-center shadow-lg",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-white text-xl"})}),(0,a.jsx)("span",{className:"text-red-700 font-bold text-lg",children:i})]}),(0,a.jsx)("button",{type:"submit",disabled:s,className:"w-full h-16 btn-primary flex items-center justify-center space-x-4 rtl:space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed text-xl font-black shadow-2xl shadow-blue-500/30 relative overflow-hidden group",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-8 h-8 border-3 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:"جاري تسجيل الدخول..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-login-box-line text-2xl group-hover:scale-110 transition-transform duration-300"}),(0,a.jsx)("span",{children:"تسجيل الدخول"})]})})]})]}),(0,a.jsxs)("div",{className:"text-center mt-12 fade-in",children:[(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 rtl:space-x-reverse mb-4",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"}),(0,a.jsx)("div",{className:"w-3 h-3 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full animate-pulse delay-200"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full animate-pulse delay-400"})]}),(0,a.jsx)("p",{className:"text-gray-600 font-bold text-lg",children:"\xa9 2024 عجائب الخبراء. جميع الحقوق محفوظة."})]})]})]})},v=e=>{let{activeSection:t,setActiveSection:s,menuItems:r}=e,[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(0);(0,l.useEffect)(()=>{let e=()=>{let e=window.scrollY;e>c&&e>100?n(!1):n(!0),d(e)};return window.addEventListener("scroll",e,{passive:!0}),()=>window.removeEventListener("scroll",e)},[c]);let o=e=>{s(e),navigator.vibrate&&navigator.vibrate(50)};return(0,a.jsxs)("nav",{className:"\n      md:hidden fixed bottom-0 left-0 right-0 z-50\n      bg-white/95 backdrop-blur-xl border-t border-gray-200/50 shadow-2xl\n      transition-transform duration-300 ease-in-out\n      ".concat(i?"translate-y-0":"translate-y-full","\n    "),children:[(0,a.jsx)("div",{className:"grid grid-cols-4 gap-1 p-2",children:r.slice(0,4).map((e,s)=>(0,a.jsxs)("button",{onClick:()=>o(e.id),className:"\n              relative flex flex-col items-center justify-center p-3 rounded-2xl \n              transition-all duration-300 transform active:scale-95\n              ".concat(t===e.id?"bg-gradient-to-br ".concat(e.color," text-white shadow-lg shadow-").concat(e.color.split("-")[1],"-500/25"):"text-gray-600 hover:text-gray-900 hover:bg-gray-100","\n            "),style:{animationDelay:"".concat(50*s,"ms")},children:[t===e.id&&(0,a.jsx)("div",{className:"absolute -top-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"}),(0,a.jsx)("i",{className:"".concat(e.icon," text-lg mb-1 transition-transform duration-300 ").concat(t===e.id?"scale-110":"")}),(0,a.jsx)("span",{className:"text-xs font-bold leading-tight text-center",children:e.name}),(0,a.jsx)("div",{className:"absolute inset-0 rounded-2xl overflow-hidden",children:(0,a.jsx)("div",{className:"absolute inset-0 bg-white/20 scale-0 rounded-2xl transition-transform duration-300 active:scale-100"})})]},e.id))}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-1 p-2 pt-0",children:r.slice(4).map((e,s)=>(0,a.jsxs)("button",{onClick:()=>o(e.id),className:"\n              relative flex flex-col items-center justify-center p-3 rounded-2xl \n              transition-all duration-300 transform active:scale-95\n              ".concat(t===e.id?"bg-gradient-to-br ".concat(e.color," text-white shadow-lg shadow-").concat(e.color.split("-")[1],"-500/25"):"text-gray-600 hover:text-gray-900 hover:bg-gray-100","\n            "),style:{animationDelay:"".concat((s+4)*50,"ms")},children:[t===e.id&&(0,a.jsx)("div",{className:"absolute -top-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"}),(0,a.jsx)("i",{className:"".concat(e.icon," text-lg mb-1 transition-transform duration-300 ").concat(t===e.id?"scale-110":"")}),(0,a.jsx)("span",{className:"text-xs font-bold leading-tight text-center",children:e.name}),(0,a.jsx)("div",{className:"absolute inset-0 rounded-2xl overflow-hidden",children:(0,a.jsx)("div",{className:"absolute inset-0 bg-white/20 scale-0 rounded-2xl transition-transform duration-300 active:scale-100"})})]},e.id))}),(0,a.jsx)("div",{className:"h-safe-area-inset-bottom"})]})};var N=s(6686);let y=(0,l.createContext)(),w=()=>{let e=(0,l.useContext)(y);if(!e)throw Error("useData must be used within a DataProvider");return e},k=e=>{let{children:t}=e,{user:s}=p(),[r,i]=(0,l.useState)(null),[n,c]=(0,l.useState)(null),[d,o]=(0,l.useState)([]),[m,x]=(0,l.useState)([]),[u,b]=(0,l.useState)(null),[j,f]=(0,l.useState)([]),[v,w]=(0,l.useState)({}),[k,C]=(0,l.useState)(!0),[_,S]=(0,l.useState)(null);(0,l.useEffect)(()=>{I()},[]);let I=async()=>{try{C(!0),S(null);let[e,t,s,a,l,r,n]=await Promise.all([(0,N.zm)(),(0,N.ub)(),(0,N.KT)(),(0,N.WH)(),(0,N.wf)(),(0,N.bW)(),(0,N.t1)()]);i(e),c(t),o(s||[]),x(a||[]),b(l),f(r||[]),w(n||{})}catch(e){S("فشل في تحميل البيانات: "+e.message),i({title:"خبرة المطابخ - مطابخ تفوق التوقعات، وخزائن بتصاميم لا تُنسى",subtitle:"نحول مساحات منزلك إلى تحف فنية تجمع بين الجمال والوظيفة بلمسة من الإبداع والحرفية العالية في خبرة المطابخ",background_image:"https://readdy.ai/api/search-image?query=luxurious%20modern%20kitchen%20with%20elegant%20design%2C%20marble%20countertops%2C%20wooden%20cabinets%2C%20high-end%20appliances%2C%20soft%20lighting%2C%20spacious%20layout%2C%20minimalist%20style%2C%20professional%20photography%2C%20high%20resolution%2C%20advertisement%20quality&width=1920&height=1080&seq=1&orientation=landscape",primary_button_text:"شاهد تصاميمنا",secondary_button_text:"تواصل معنا"}),b({socialMedia:[{platform:"twitter",url:g.twitter,icon:"ri-twitter-line"},{platform:"snapchat",url:g.snapchat,icon:"ri-snapchat-line"},{platform:"instagram",url:g.instagram,icon:"ri-instagram-line"},{platform:"whatsapp",url:g.whatsapp,icon:"ri-whatsapp-line"},{platform:"tiktok",url:g.tiktok,icon:"ri-tiktok-line"}],quickLinks:[{href:"#home",text:"الرئيسية"},{href:"#why-us",text:"لماذا نحن"},{href:"#kitchens",text:"المطابخ"},{href:"#cabinets",text:"الخزائن"},{href:"#contact",text:"تواصل معنا"}],contactInfo:[{icon:"ri-map-pin-line",text:h.address},{icon:"ri-phone-line",text:h.phone},{icon:"ri-mail-line",text:h.email},{icon:"ri-time-line",text:"السبت - الخميس: 9 صباحاً - 9 مساءً"}],copyright:"\xa9 2024 ".concat(h.name,". جميع الحقوق محفوظة.")})}finally{C(!1)}},E=async e=>{try{await (0,N.aU)(e,null==s?void 0:s.id);let t=await (0,N.zm)();return i(t),localStorage.setItem("hero_data_updated",Date.now().toString()),{success:!0}}catch(e){throw Error("فشل في تحديث بيانات البانر الرئيسي")}},P=async e=>{try{return await (0,N.ro)(e,null==s?void 0:s.id),c({...n,...e}),{success:!0}}catch(e){throw Error("فشل في تحديث بيانات لماذا تختارنا")}},T=async e=>{try{let t=await (0,N.It)(e,null==s?void 0:s.id),a=await (0,N.KT)();return o(a),t}catch(e){throw Error("فشل في إضافة المطبخ")}},L=async(e,t)=>{try{await (0,N.Cm)(e,t,null==s?void 0:s.id);let a=await (0,N.KT)();return o(a),{success:!0}}catch(e){throw Error("فشل في تحديث المطبخ")}},A=async e=>{try{return await (0,N.y9)(e,null==s?void 0:s.id),o(d.filter(t=>t.id!==e)),{success:!0}}catch(e){throw Error("فشل في حذف المطبخ")}},M=async e=>{try{let t=await (0,N.yI)(e,null==s?void 0:s.id),a=await (0,N.WH)();return x(a),t}catch(e){throw Error("فشل في إضافة الخزانة")}},B=async(e,t)=>{try{await (0,N.sB)(e,t,null==s?void 0:s.id);let a=await (0,N.WH)();return x(a),{success:!0}}catch(e){throw Error("فشل في تحديث الخزانة")}},U=async e=>{try{return await (0,N.OX)(e,null==s?void 0:s.id),x(m.filter(t=>t.id!==e)),{success:!0}}catch(e){throw Error("فشل في حذف الخزانة")}},D=async e=>{try{return await (0,N.dt)(e,null==s?void 0:s.id),b({...u,...e}),{success:!0}}catch(e){throw Error("فشل في تحديث بيانات التذييل")}},O=async(e,t)=>{try{return await (0,N.$L)(e,t,null==s?void 0:s.id),w({...v,[e]:t}),{success:!0}}catch(e){throw Error("فشل في تحديث إعدادات الشركة")}},X=async()=>{await I()};return(0,a.jsx)(y.Provider,{value:{heroData:r,whyChooseUsData:n,kitchensData:d,cabinetsData:m,footerData:u,categories:j,companySettings:v,loading:k,error:_,updateHero:E,setHeroData:i,updateWhyChooseUs:P,setWhyChooseUsData:c,addNewKitchen:T,updateExistingKitchen:L,removeKitchen:A,setKitchensData:o,addNewCabinet:M,updateExistingCabinet:B,removeCabinet:U,setCabinetsData:x,updateFooter:D,setFooterData:b,updateSetting:O,refreshData:X,refreshKitchens:async()=>{o(await (0,N.KT)()||[])},refreshCabinets:async()=>{x(await (0,N.WH)()||[])},resetError:()=>{S(null)}},children:t})},C=()=>{let{heroData:e,updateHero:t,loading:s,error:r}=(0,l.useContext)(y),[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)({title:"",subtitle:"",backgroundImage:"",primaryButtonText:"",secondaryButtonText:""}),[o,m]=(0,l.useState)(!1);(0,l.useEffect)(()=>{e&&d({title:e.title||"",subtitle:e.subtitle||"",backgroundImage:e.background_image||"",primaryButtonText:e.primary_button_text||"",secondaryButtonText:e.secondary_button_text||""})},[e]);let x=e=>{d({...c,[e.target.name]:e.target.value})},u=async()=>{m(!0);try{let e={title:c.title,subtitle:c.subtitle,background_image:c.backgroundImage,primary_button_text:c.primaryButtonText,secondary_button_text:c.secondaryButtonText};await t(e),n(!1),alert("تم حفظ التغييرات بنجاح!")}catch(e){alert(e.message||"حدث خطأ أثناء الحفظ")}finally{m(!1)}},h=async e=>{let t=e.target.files[0];if(t){if(t.size>2097152)return void alert("حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت");if(!["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(t.type))return void alert("نوع الملف غير مدعوم. يُسمح برفع الصور فقط (JPEG, PNG, WebP, GIF)");try{m(!0);let e=new FormData;e.append("image",t);let s=await fetch("/api/upload/hero",{method:"POST",body:e}),a=await s.json();s.ok&&a.success?(d({...c,backgroundImage:a.imagePath}),alert("تم رفع الصورة بنجاح (".concat(a.sizeFormatted||"حجم غير محدد",")"))):alert(a.error||"فشل في رفع الصورة. يرجى المحاولة مرة أخرى.")}catch(e){alert("حدث خطأ أثناء رفع الصورة. تحقق من الاتصال بالإنترنت.")}finally{m(!1)}}};return s?(0,a.jsx)("div",{className:"admin-dashboard space-y-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"spinner w-8 h-8"}),(0,a.jsx)("span",{className:"mr-3",children:"جاري تحميل البيانات..."})]})}):r?(0,a.jsx)("div",{className:"admin-dashboard space-y-8",children:(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 text-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-3xl text-red-500 mb-3"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-red-800 mb-2",children:"خطأ في تحميل البيانات"}),(0,a.jsx)("p",{className:"text-red-600",children:r})]})}):(0,a.jsxs)("div",{className:"admin-dashboard space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"إدارة قسم الهيرو"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"تحرير النصوص والصور في القسم الرئيسي للموقع"})]}),(0,a.jsx)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{e&&d({title:e.title||"",subtitle:e.subtitle||"",backgroundImage:e.background_image||"",primaryButtonText:e.primary_button_text||"",secondaryButtonText:e.secondary_button_text||""}),n(!1)},className:"btn-secondary",disabled:o,children:"إلغاء"}),(0,a.jsx)("button",{onClick:u,className:"btn-primary",disabled:o,children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-4 h-4 ml-2"}),"جاري الحفظ..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-save-line ml-2"}),"حفظ التغييرات"]})})]}):(0,a.jsxs)("button",{onClick:()=>n(!0),className:"btn-primary",children:[(0,a.jsx)("i",{className:"ri-edit-line ml-2"}),"تعديل"]})})]}),(0,a.jsxs)("div",{className:"admin-card overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("h2",{className:"text-lg font-bold text-gray-900 flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-eye-line text-blue-600"}),(0,a.jsx)("span",{children:"معاينة القسم"})]})}),(0,a.jsx)("div",{className:"relative h-96 bg-cover bg-center flex items-center",style:{backgroundImage:"linear-gradient(to left, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7)), url(".concat(c.backgroundImage,")")},children:(0,a.jsx)("div",{className:"container mx-auto px-6 text-white",children:(0,a.jsxs)("div",{className:"max-w-4xl",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6 leading-tight",children:c.title}),(0,a.jsx)("p",{className:"text-xl text-gray-100 mb-8 leading-relaxed",children:c.subtitle}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-all duration-300",children:c.primaryButtonText}),(0,a.jsx)("button",{className:"bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-white px-8 py-4 rounded-lg text-lg font-medium transition-all duration-300",children:c.secondaryButtonText})]})]})})})]}),i&&(0,a.jsxs)("div",{className:"admin-card p-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-edit-line text-blue-600"}),(0,a.jsx)("span",{children:"تحرير المحتوى"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العنوان الرئيسي"}),(0,a.jsx)("input",{type:"text",name:"title",value:c.title,onChange:x,className:"form-input",placeholder:"أدخل العنوان الرئيسي"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العنوان الفرعي"}),(0,a.jsx)("textarea",{name:"subtitle",value:c.subtitle,onChange:x,rows:3,className:"form-textarea",placeholder:"أدخل العنوان الفرعي"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"نص الزر الأساسي"}),(0,a.jsx)("input",{type:"text",name:"primaryButtonText",value:c.primaryButtonText,onChange:x,className:"form-input",placeholder:"أدخل نص الزر الأساسي"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"نص الزر الثانوي"}),(0,a.jsx)("input",{type:"text",name:"secondaryButtonText",value:c.secondaryButtonText,onChange:x,className:"form-input",placeholder:"أدخل نص الزر الثانوي"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"صورة الخلفية"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("input",{type:"url",name:"backgroundImage",value:c.backgroundImage,onChange:x,className:"form-input",placeholder:"أدخل رابط الصورة"}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"أو"})}),(0,a.jsxs)("div",{className:"image-upload-area",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:h,className:"hidden",id:"hero-image-upload"}),(0,a.jsx)("label",{htmlFor:"hero-image-upload",className:"cursor-pointer",children:o?(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mb-4"}),(0,a.jsx)("p",{className:"text-blue-600 font-medium",children:"جاري رفع الصورة..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-upload-cloud-line text-4xl text-gray-400 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 font-medium",children:"اضغط لرفع صورة جديدة"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"PNG, JPG, WebP, GIF حتى 2MB"})]})})]})]})]})]})]}),!i&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-information-line text-blue-600"}),(0,a.jsx)("span",{children:"البيانات الحالية"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"العنوان الرئيسي"}),(0,a.jsx)("p",{className:"text-gray-900 bg-gray-50 p-3 rounded-lg",children:e.title})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"العنوان الفرعي"}),(0,a.jsx)("p",{className:"text-gray-900 bg-gray-50 p-3 rounded-lg",children:e.subtitle})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"نص الزر الأساسي"}),(0,a.jsx)("p",{className:"text-gray-900 bg-gray-50 p-3 rounded-lg",children:e.primaryButtonText})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"نص الزر الثانوي"}),(0,a.jsx)("p",{className:"text-gray-900 bg-gray-50 p-3 rounded-lg",children:e.secondaryButtonText})]})]})]})]})]})},_=()=>{let{whyChooseUsData:e,updateWhyChooseUs:t,loading:s,error:r}=(0,l.useContext)(y),[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)(null),[o,m]=(0,l.useState)({title:"",subtitle:"",features:[]}),[x,u]=(0,l.useState)(!1);(0,l.useEffect)(()=>{e&&m({title:e.title||"",subtitle:e.subtitle||"",features:e.features||[]})},[e]);let h=e=>{m({...o,[e.target.name]:e.target.value})},g=(e,t,s)=>{let a=[...o.features];a[e]={...a[e],[t]:s},m({...o,features:a})},b=async()=>{u(!0);try{await t(o),n(!1),d(null),alert("تم حفظ التغييرات بنجاح!")}catch(e){alert(e.message||"حدث خطأ أثناء الحفظ")}finally{u(!1)}},p=[{value:"from-blue-500 to-cyan-500",label:"أزرق إلى سماوي",bg:"from-blue-50 to-cyan-50"},{value:"from-purple-500 to-pink-500",label:"بنفسجي إلى وردي",bg:"from-purple-50 to-pink-50"},{value:"from-green-500 to-emerald-500",label:"أخضر إلى زمردي",bg:"from-green-50 to-emerald-50"},{value:"from-orange-500 to-red-500",label:"برتقالي إلى أحمر",bg:"from-orange-50 to-red-50"},{value:"from-yellow-500 to-orange-500",label:"أصفر إلى برتقالي",bg:"from-yellow-50 to-orange-50"}],j=["ri-award-line","ri-palette-line","ri-tools-line","ri-star-line","ri-heart-line","ri-shield-check-line","ri-time-line","ri-user-line","ri-settings-line","ri-lightbulb-line"];return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:'إدارة قسم "لماذا تختارنا"'}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"تحرير عنوان القسم والمميزات"})]}),(0,a.jsx)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{e&&m({title:e.title||"",subtitle:e.subtitle||"",features:e.features||[]}),n(!1),d(null)},className:"btn-secondary",disabled:x,children:"إلغاء"}),(0,a.jsx)("button",{onClick:b,className:"btn-primary",disabled:x,children:x?"جاري الحفظ...":"حفظ التغييرات"})]}):(0,a.jsxs)("button",{onClick:()=>n(!0),className:"btn-primary",children:[(0,a.jsx)("i",{className:"ri-edit-line ml-2"}),"تعديل"]})})]}),i&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-6",children:"تحرير عنوان القسم"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العنوان الرئيسي"}),(0,a.jsx)("input",{type:"text",name:"title",value:o.title,onChange:h,className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العنوان الفرعي"}),(0,a.jsx)("textarea",{name:"subtitle",value:o.subtitle,onChange:h,rows:2,className:"form-textarea"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-bold text-gray-900",children:"إدارة المميزات"}),i&&(0,a.jsxs)("button",{onClick:()=>{let e={id:Date.now(),icon:"ri-star-line",title:"ميزة جديدة",description:"وصف الميزة الجديدة",gradient:"from-blue-500 to-cyan-500",bgGradient:"from-blue-50 to-cyan-50",number:"1",subtitle:"وصف قصير"};m({...o,features:[...o.features,e]})},className:"btn-success",children:[(0,a.jsx)("i",{className:"ri-add-line ml-2"}),"إضافة ميزة جديدة"]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.features.map((e,t)=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-xl p-6 relative",children:[i&&(0,a.jsxs)("div",{className:"absolute top-2 left-2 flex space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("button",{onClick:()=>d(c===t?null:t),className:"w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center hover:bg-blue-200",children:(0,a.jsx)("i",{className:"ri-edit-line text-sm"})}),(0,a.jsx)("button",{onClick:()=>(e=>{if(window.confirm("هل أنت متأكد من حذف هذه الميزة؟")){let t=o.features.filter((t,s)=>s!==e);m({...o,features:t})}})(t),className:"w-8 h-8 bg-red-100 text-red-600 rounded-full flex items-center justify-center hover:bg-red-200",children:(0,a.jsx)("i",{className:"ri-delete-bin-line text-sm"})})]}),c===t?(0,a.jsxs)("div",{className:"space-y-4 mt-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الأيقونة"}),(0,a.jsx)("select",{value:e.icon,onChange:e=>g(t,"icon",e.target.value),className:"form-input",children:j.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العنوان"}),(0,a.jsx)("input",{type:"text",value:e.title,onChange:e=>g(t,"title",e.target.value),className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف"}),(0,a.jsx)("textarea",{value:e.description,onChange:e=>g(t,"description",e.target.value),rows:3,className:"form-textarea"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الرقم"}),(0,a.jsx)("input",{type:"text",value:e.number,onChange:e=>g(t,"number",e.target.value),className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف القصير"}),(0,a.jsx)("input",{type:"text",value:e.subtitle,onChange:e=>g(t,"subtitle",e.target.value),className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اللون"}),(0,a.jsx)("select",{value:e.gradient,onChange:e=>{let s=p.find(t=>t.value===e.target.value);g(t,"gradient",e.target.value),g(t,"bgGradient",s.bg)},className:"form-input",children:p.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"w-16 h-16 mx-auto bg-gradient-to-r ".concat(e.gradient," rounded-2xl flex items-center justify-center mb-4 relative"),children:[(0,a.jsx)("i",{className:"".concat(e.icon," text-2xl text-white")}),(0,a.jsx)("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-gray-50",children:(0,a.jsx)("span",{className:"text-xs font-bold bg-gradient-to-r ".concat(e.gradient," bg-clip-text text-transparent"),children:e.number})})]}),(0,a.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.subtitle})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-6",children:"معاينة القسم"}),(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:o.title}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:o.subtitle})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:o.features.map((e,t)=>(0,a.jsxs)("div",{className:"text-center p-6 bg-gray-50 rounded-xl",children:[(0,a.jsxs)("div",{className:"w-20 h-20 mx-auto bg-gradient-to-r ".concat(e.gradient," rounded-2xl flex items-center justify-center mb-6 relative"),children:[(0,a.jsx)("i",{className:"".concat(e.icon," text-3xl text-white")}),(0,a.jsx)("div",{className:"absolute -top-2 -right-2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-4 border-gray-50",children:(0,a.jsx)("span",{className:"text-sm font-bold bg-gradient-to-r ".concat(e.gradient," bg-clip-text text-transparent"),children:e.number})})]}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.subtitle})]},e.id))})]})]})},S=e=>{let{isOpen:t,onClose:s,title:r,children:i,size:n="lg",showCloseButton:c=!0,closeOnOverlayClick:d=!0,closeOnEscape:o=!0}=e;return((0,l.useEffect)(()=>{if(t){document.body.classList.add("modal-open");let e=e=>{"Escape"===e.key&&o&&s()};return o&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}return()=>{document.body.classList.remove("modal-open")}},[t,s,o]),t)?(0,a.jsx)("div",{className:"modal-overlay",onClick:e=>{e.target===e.currentTarget&&d&&s()},style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.9)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:999999,padding:"20px",backdropFilter:"blur(12px)",overflow:"auto",animation:"modalOverlayFadeIn 0.3s ease-out"},children:(0,a.jsxs)("div",{className:"modal-content ".concat({sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"}[n]),onClick:e=>e.stopPropagation(),style:{background:"white",borderRadius:"24px",boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.6)",width:"95%",maxHeight:"90vh",overflowY:"auto",animation:"modalSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)",position:"relative",transform:"translateZ(0)",margin:"0 auto",border:"2px solid rgba(255, 255, 255, 0.2)"},children:[(r||c)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[r&&(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:r}),c&&(0,a.jsx)("button",{onClick:s,className:"w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors duration-200 ml-auto","aria-label":"إغلاق",children:(0,a.jsx)("i",{className:"ri-close-line text-gray-600 text-lg"})})]}),(0,a.jsx)("div",{className:"modal-body",children:i})]})}):null},I=()=>{let{kitchensData:e,addNewKitchen:t,updateExistingKitchen:s,removeKitchen:r,categories:i}=w(),[n,c]=(0,l.useState)(!1),[d,o]=(0,l.useState)(null),[m,x]=(0,l.useState)({title:"",description:"",category:"modern-kitchens",categoryId:null,images:[""]}),[u,h]=(0,l.useState)(!1),[g,b]=(0,l.useState)({}),p=i.filter(e=>"kitchen"===e.type),j=e=>e?"string"==typeof e?e:"object"==typeof e&&e.image_url?e.image_url:null:null,f=e=>{x({...m,[e.target.name]:e.target.value})},v=(e,t)=>{let s=[...m.images];s[e]=t,x({...m,images:s})},N=async(e,t)=>{let s=t.target.files[0];if(s){if(s.size>2097152)return void alert("حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت");if(!["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(s.type))return void alert("نوع الملف غير مدعوم. يُسمح برفع الصور فقط (JPEG, PNG, WebP, GIF)");try{b(t=>({...t,[e]:!0}));let t=new FormData;t.append("image",s);let a=await fetch("/api/upload/kitchens",{method:"POST",body:t}),l=await a.json();a.ok&&l.success?(v(e,l.imagePath),alert("تم رفع الصورة بنجاح (".concat(l.sizeFormatted||"حجم غير محدد",")"))):alert(l.error||"فشل في رفع الصورة. يرجى المحاولة مرة أخرى.")}catch(e){alert("حدث خطأ أثناء رفع الصورة. تحقق من الاتصال بالإنترنت.")}finally{b(t=>{let s={...t};return delete s[e],s})}}},y=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e?(o(e),x({title:e.title,description:e.description,category:e.category,images:e.images&&e.images.length>0?e.images.map(e=>"string"==typeof e?e:e.image_url||""):[""]})):(o(null),x({title:"",description:"",category:"modern-kitchens",images:[""]})),c(!0)},k=()=>{c(!1),o(null),x({title:"",description:"",category:"modern-kitchens",images:[""]})},C=async()=>{let e=m.images.filter(e=>"string"==typeof e&&""!==e.trim());if(0===e.length)return void alert("يرجى إضافة صورة واحدة على الأقل");if(!m.category)return void alert("يرجى تحديد فئة المطبخ");h(!0);try{let a=p.find(e=>e.slug===m.category),l=(null==a?void 0:a.name)||m.category,r={title:m.title.trim()||"مطبخ ".concat(l),description:m.description.trim()||"",category_id:null==a?void 0:a.id,category:m.category,images:e,isFeatured:!1};d?await s(d.id,r):await t(r),k(),alert("تم حفظ المطبخ بنجاح!")}catch(e){alert(e.message||"حدث خطأ أثناء الحفظ")}finally{h(!1)}},_=async e=>{if(window.confirm("هل أنت متأكد من حذف هذا المطبخ؟"))try{await r(e),alert("تم حذف المطبخ بنجاح!")}catch(e){alert(e.message||"حدث خطأ أثناء الحذف")}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"إدارة المطابخ"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"إضافة وتعديل وحذف تصاميم المطابخ"})]}),(0,a.jsxs)("button",{onClick:()=>y(),className:"btn-primary",children:[(0,a.jsx)("i",{className:"ri-add-line ml-2"}),"إضافة مطبخ جديد"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي المطابخ"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.length})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-home-4-line text-2xl text-blue-600"})})]})}),i.map(t=>(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:t.name}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.filter(e=>e.category===t.id).length})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-layout-line text-2xl text-purple-600"})})]})},t.id))]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:null==e?void 0:e.map(e=>{var t,s,l,r;return(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"relative h-48",children:[(0,a.jsx)("img",{src:j(null==(t=e.images)?void 0:t[0])||"https://via.placeholder.com/400x300?text=No+Image",alt:e.title,className:"w-full h-full object-cover"}),(0,a.jsx)("div",{className:"absolute top-3 right-3",children:(0,a.jsx)("span",{className:"badge badge-info",children:(e=>{let t=p.find(t=>t.slug===e);return t?t.name:e})(e.category)})}),(0,a.jsxs)("div",{className:"absolute top-3 left-3 flex space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("button",{onClick:()=>y(e),className:"w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors",children:(0,a.jsx)("i",{className:"ri-edit-line text-blue-600"})}),(0,a.jsx)("button",{onClick:()=>_(e.id),className:"w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors",children:(0,a.jsx)("i",{className:"ri-delete-bin-line text-red-600"})})]})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[(null==(s=e.images)?void 0:s.length)||0," صورة"]}),(0,a.jsxs)("div",{className:"flex space-x-2 rtl:space-x-reverse",children:[null==(l=e.images)?void 0:l.slice(0,3).map((e,t)=>(0,a.jsx)("div",{className:"w-8 h-8 rounded border-2 border-white shadow-sm overflow-hidden",children:(0,a.jsx)("img",{src:j(e),alt:"",className:"w-full h-full object-cover"})},t)),(null==(r=e.images)?void 0:r.length)>3&&(0,a.jsx)("div",{className:"w-8 h-8 rounded border-2 border-white shadow-sm bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:["+",e.images.length-3]})})]})]})]})]},e.id)})}),(null==e?void 0:e.length)===0&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"ri-home-4-line text-4xl text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد مطابخ"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"ابدأ بإضافة أول تصميم مطبخ"}),(0,a.jsx)("button",{onClick:()=>y(),className:"btn-primary",children:"إضافة مطبخ جديد"})]}),(0,a.jsxs)(S,{isOpen:n,onClose:k,title:d?"تعديل المطبخ":"إضافة مطبخ جديد",size:"lg",children:[(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"عنوان المطبخ (اختياري)"}),(0,a.jsx)("input",{type:"text",name:"title",value:m.title,onChange:f,className:"form-input",placeholder:"أدخل عنوان المطبخ (سيتم إنشاء عنوان تلقائي إذا ترك فارغاً)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف (اختياري)"}),(0,a.jsx)("textarea",{name:"description",value:m.description,onChange:f,rows:3,className:"form-textarea",placeholder:"أدخل وصف المطبخ (اختياري)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الفئة *"}),(0,a.jsx)("select",{name:"category",value:m.category,onChange:f,className:"form-input",children:p.map(e=>(0,a.jsx)("option",{value:e.slug,children:e.name},e.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الصور *"}),(0,a.jsxs)("div",{className:"space-y-4",children:[m.images.map((e,t)=>(0,a.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("input",{type:"url",value:e,onChange:e=>v(t,e.target.value),className:"form-input",placeholder:"رابط الصورة"})}),(0,a.jsxs)("div",{className:"flex space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>N(t,e),className:"hidden",id:"image-upload-".concat(t)}),(0,a.jsx)("label",{htmlFor:"image-upload-".concat(t),className:"btn-secondary cursor-pointer ".concat(g[t]?"opacity-50 cursor-not-allowed":""),children:g[t]?(0,a.jsx)("i",{className:"ri-loader-4-line animate-spin"}):(0,a.jsx)("i",{className:"ri-upload-line"})}),m.images.length>1&&(0,a.jsx)("button",{type:"button",onClick:()=>(e=>{let t=m.images.filter((t,s)=>s!==e);x({...m,images:t})})(t),className:"btn-danger",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})]},t)),(0,a.jsxs)("button",{type:"button",onClick:()=>{x({...m,images:[...m.images,""]})},className:"btn-secondary w-full",children:[(0,a.jsx)("i",{className:"ri-add-line ml-2"}),"إضافة صورة أخرى"]})]})]})]}),(0,a.jsxs)("div",{className:"p-6 border-t border-gray-200 flex justify-end space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("button",{onClick:k,className:"btn-secondary",disabled:u,children:"إلغاء"}),(0,a.jsx)("button",{onClick:C,className:"btn-primary",disabled:u,children:u?"جاري الحفظ...":"حفظ"})]})]})]})};var E=s(4805);let P=()=>{let{cabinetsData:e,addNewCabinet:t,updateExistingCabinet:s,removeCabinet:r,categories:i,refreshCabinets:n}=(0,l.useContext)(y),[c,d]=(0,l.useState)([]),[o,m]=(0,l.useState)(!1),[x,u]=(0,l.useState)(null),[h,g]=(0,l.useState)({title:"",description:"",category:"modern-cabinets",images:[""]}),[b,p]=(0,l.useState)(!1),[j,f]=(0,l.useState)({}),[v,N]=(0,l.useState)(""),[w,k]=(0,l.useState)(1);(0,l.useEffect)(()=>{let t="admin_cabinets_data",s=E.yk.get(t);!s||(null==e?void 0:e.length)?(null==e?void 0:e.length)&&(d(e),E.yk.set(t,e,3e5)):d(s)},[e]);let C=(0,l.useCallback)((0,E.sg)(e=>{N(e),k(1)},300),[]),_=(0,l.useMemo)(()=>v?c.filter(e=>{var t,s,a;return(null==(t=e.title)?void 0:t.toLowerCase().includes(v.toLowerCase()))||(null==(s=e.description)?void 0:s.toLowerCase().includes(v.toLowerCase()))||(null==(a=A(e.category))?void 0:a.toLowerCase().includes(v.toLowerCase()))}):c,[c,v]),I=(0,l.useMemo)(()=>{let e=(w-1)*12;return _.slice(e,e+12)},[_,w,12]),P=Math.ceil(_.length/12),T=i.filter(e=>"cabinet"===e.type),L=e=>e?"string"==typeof e?e:"object"==typeof e&&e.image_url?e.image_url:null:null,A=e=>{let t=T.find(t=>t.slug===e);return t?t.name:e},M=e=>{g({...h,[e.target.name]:e.target.value})},B=(e,t)=>{let s=[...h.images];s[e]=t,g({...h,images:s})},U=async(e,t)=>{let s=t.target.files[0];if(s){if(s.size>2097152)return void alert("حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت");if(!["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(s.type))return void alert("نوع الملف غير مدعوم. يُسمح برفع الصور فقط (JPEG, PNG, WebP, GIF)");try{f(t=>({...t,[e]:!0}));let t=new FormData;t.append("image",s);let a=await fetch("/api/upload/cabinets",{method:"POST",body:t}),l=await a.json();a.ok&&l.success?(B(e,l.imagePath),alert("تم رفع الصورة بنجاح (".concat(l.sizeFormatted||"حجم غير محدد",")"))):alert(l.error||"فشل في رفع الصورة. يرجى المحاولة مرة أخرى.")}catch(e){alert("حدث خطأ أثناء رفع الصورة. تحقق من الاتصال بالإنترنت.")}finally{f(t=>{let s={...t};return delete s[e],s})}}},D=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e?(u(e),g({title:e.title,description:e.description,category:e.category_slug||e.category,images:e.images&&e.images.length>0?e.images.map(e=>"string"==typeof e?e:e.image_url||""):[""]})):(u(null),g({title:"",description:"",category:"modern-cabinets",images:[""]})),m(!0)},O=()=>{m(!1),u(null),g({title:"",description:"",category:"modern-cabinets",images:[""]})},X=async()=>{let e=h.images.filter(e=>"string"==typeof e&&""!==e.trim());if(0===e.length)return void alert("يرجى إضافة صورة واحدة على الأقل");if(!h.category)return void alert("يرجى تحديد فئة الخزانة");p(!0);try{let a=T.find(e=>e.slug===h.category),l=(null==a?void 0:a.name)||h.category,r={title:h.title.trim()||"خزانة ".concat(l),description:h.description.trim()||"",category_id:a?a.id:null,images:e,is_featured:!1,sort_order:0};x?(await s(x.id,r),alert("تم تحديث الخزانة بنجاح!")):(await t(r),alert("تم إضافة الخزانة بنجاح!")),await n(),O()}catch(e){alert("حدث خطأ أثناء الحفظ")}finally{p(!1)}},F=async e=>{if(window.confirm("هل أنت متأكد من حذف هذه الخزانة؟"))try{await r(e),await n(),alert("تم حذف الخزانة بنجاح!")}catch(e){alert("حدث خطأ أثناء الحذف")}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"إدارة الخزائن"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"إضافة وتعديل وحذف تصاميم الخزائن"})]}),(0,a.jsxs)("button",{onClick:()=>D(),className:"btn-primary",children:[(0,a.jsx)("i",{className:"ri-add-line ml-2"}),"إضافة خزانة جديدة"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الخزائن"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:(null==c?void 0:c.length)||0})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-archive-line text-2xl text-purple-600"})})]})}),T.map(e=>(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.name}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:c.filter(t=>t.category_id===e.id).length})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-layout-line text-2xl text-green-600"})})]})},e.id))]}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[(0,a.jsx)("div",{className:"flex-1 max-w-md",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("i",{className:"ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"البحث في الخزائن...",className:"form-input pr-10",onChange:e=>C(e.target.value)})]})}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["عرض ",I.length," من ",_.length," خزانة"]})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:null==I?void 0:I.map(e=>{var t,s,l;return(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"relative h-48",children:[(0,a.jsx)("img",{src:L(null==(t=e.images)?void 0:t[0])||"https://via.placeholder.com/400x300?text=No+Image",alt:e.title,className:"w-full h-full object-cover"}),(0,a.jsx)("div",{className:"absolute top-3 right-3",children:(0,a.jsx)("span",{className:"badge badge-info",children:e.category_name||A(e.category)})}),(0,a.jsxs)("div",{className:"absolute top-3 left-3 flex space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("button",{onClick:()=>D(e),className:"w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors",children:(0,a.jsx)("i",{className:"ri-edit-line text-blue-600"})}),(0,a.jsx)("button",{onClick:()=>F(e.id),className:"w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors",children:(0,a.jsx)("i",{className:"ri-delete-bin-line text-red-600"})})]})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[(null==(s=e.images)?void 0:s.length)||0," صورة"]}),(0,a.jsxs)("div",{className:"flex space-x-2 rtl:space-x-reverse",children:[null==(l=e.images)?void 0:l.slice(0,3).map((e,t)=>(0,a.jsx)("div",{className:"w-8 h-8 rounded border-2 border-white shadow-sm overflow-hidden",children:(0,a.jsx)("img",{src:L(e),alt:"",className:"w-full h-full object-cover"})},t)),e.images&&e.images.length>3&&(0,a.jsx)("div",{className:"w-8 h-8 rounded border-2 border-white shadow-sm bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:["+",e.images.length-3]})})]})]})]})]},e.id)})}),P>1&&(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["صفحة ",w," من ",P]}),(0,a.jsxs)("div",{className:"flex space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("button",{onClick:()=>k(e=>Math.max(e-1,1)),disabled:1===w,className:"px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"السابق"}),[...Array(P)].map((e,t)=>{let s=t+1;return s===w||1===s||s===P||s>=w-1&&s<=w+1?(0,a.jsx)("button",{onClick:()=>k(s),className:"px-3 py-2 text-sm border rounded-lg ".concat(w===s?"bg-blue-600 text-white border-blue-600":"border-gray-300 hover:bg-gray-50"),children:s},s):s===w-2||s===w+2?(0,a.jsx)("span",{className:"px-2 text-gray-400",children:"..."},s):null}),(0,a.jsx)("button",{onClick:()=>k(e=>Math.min(e+1,P)),disabled:w===P,className:"px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"التالي"})]})]})}),(null==c?void 0:c.length)===0&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"ri-archive-line text-4xl text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد خزائن"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"ابدأ بإضافة أول تصميم خزانة"}),(0,a.jsx)("button",{onClick:()=>D(),className:"btn-primary",children:"إضافة خزانة جديدة"})]}),(0,a.jsxs)(S,{isOpen:o,onClose:O,title:x?"تعديل الخزانة":"إضافة خزانة جديدة",size:"lg",children:[(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"عنوان الخزانة (اختياري)"}),(0,a.jsx)("input",{type:"text",name:"title",value:h.title,onChange:M,className:"form-input",placeholder:"أدخل عنوان الخزانة (سيتم إنشاء عنوان تلقائي إذا ترك فارغاً)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف (اختياري)"}),(0,a.jsx)("textarea",{name:"description",value:h.description,onChange:M,rows:3,className:"form-textarea",placeholder:"أدخل وصف الخزانة (اختياري)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الفئة *"}),(0,a.jsx)("select",{name:"category",value:h.category,onChange:M,className:"form-input",children:T.map(e=>(0,a.jsx)("option",{value:e.slug,children:e.name},e.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الصور *"}),(0,a.jsxs)("div",{className:"space-y-4",children:[h.images.map((e,t)=>(0,a.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("input",{type:"url",value:e,onChange:e=>B(t,e.target.value),className:"form-input",placeholder:"رابط الصورة"})}),(0,a.jsxs)("div",{className:"flex space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>U(t,e),className:"hidden",id:"cabinet-image-upload-".concat(t)}),(0,a.jsx)("label",{htmlFor:"cabinet-image-upload-".concat(t),className:"btn-secondary cursor-pointer ".concat(j[t]?"opacity-50 cursor-not-allowed":""),children:j[t]?(0,a.jsx)("i",{className:"ri-loader-4-line animate-spin"}):(0,a.jsx)("i",{className:"ri-upload-line"})}),h.images.length>1&&(0,a.jsx)("button",{type:"button",onClick:()=>(e=>{let t=h.images.filter((t,s)=>s!==e);g({...h,images:t})})(t),className:"btn-danger",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})]},t)),(0,a.jsxs)("button",{type:"button",onClick:()=>{g({...h,images:[...h.images,""]})},className:"btn-secondary w-full",children:[(0,a.jsx)("i",{className:"ri-add-line ml-2"}),"إضافة صورة أخرى"]})]})]})]}),(0,a.jsxs)("div",{className:"p-6 border-t border-gray-200 flex justify-end space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("button",{onClick:O,className:"btn-secondary",disabled:b,children:"إلغاء"}),(0,a.jsx)("button",{onClick:X,className:"btn-primary",disabled:b,children:b?"جاري الحفظ...":"حفظ"})]})]})]})},T=()=>{let{footerData:e,updateFooter:t,loading:s,error:r}=(0,l.useContext)(y),[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)({socialMedia:[],quickLinks:[],contactInfo:[],copyright:""}),[o,m]=(0,l.useState)(!1);(0,l.useEffect)(()=>{e&&d({socialMedia:e.socialMedia||[],quickLinks:e.quickLinks||[],contactInfo:e.contactInfo||[],copyright:e.copyright||""})},[e]);let x=(e,t,s)=>{let a=[...c.socialMedia];a[e]={...a[e],[t]:s},d({...c,socialMedia:a})},u=(e,t,s)=>{let a=[...c.quickLinks];a[e]={...a[e],[t]:s},d({...c,quickLinks:a})},h=(e,t,s)=>{let a=[...c.contactInfo];a[e]={...a[e],[t]:s},d({...c,contactInfo:a})},g=async()=>{m(!0);try{await t(c),n(!1),alert("تم حفظ التغييرات بنجاح!")}catch(e){alert(e.message||"حدث خطأ أثناء الحفظ")}finally{m(!1)}},b=[{value:"ri-twitter-line",label:"تويتر"},{value:"ri-snapchat-line",label:"سناب شات"},{value:"ri-instagram-line",label:"إنستغرام"},{value:"ri-whatsapp-line",label:"واتساب"},{value:"ri-tiktok-line",label:"تيك توك"},{value:"ri-facebook-line",label:"فيسبوك"},{value:"ri-youtube-line",label:"يوتيوب"},{value:"ri-linkedin-line",label:"لينكد إن"}],p=[{value:"ri-map-pin-line",label:"الموقع"},{value:"ri-phone-line",label:"الهاتف"},{value:"ri-mail-line",label:"البريد الإلكتروني"},{value:"ri-time-line",label:"أوقات العمل"},{value:"ri-information-line",label:"معلومات عامة"}];return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"إدارة الفوتر"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"تحرير معلومات التواصل والروابط في أسفل الموقع"})]}),(0,a.jsx)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{e&&d({socialMedia:e.socialMedia||[],quickLinks:e.quickLinks||[],contactInfo:e.contactInfo||[],copyright:e.copyright||""}),n(!1)},className:"btn-secondary",disabled:o,children:"إلغاء"}),(0,a.jsx)("button",{onClick:g,className:"btn-primary",disabled:o,children:o?"جاري الحفظ...":"حفظ التغييرات"})]}):(0,a.jsxs)("button",{onClick:()=>n(!0),className:"btn-primary",children:[(0,a.jsx)("i",{className:"ri-edit-line ml-2"}),"تعديل"]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-gray-900 flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-share-line text-blue-600"}),(0,a.jsx)("span",{children:"مواقع التواصل الاجتماعي"})]}),i&&(0,a.jsxs)("button",{onClick:()=>{d({...c,socialMedia:[...c.socialMedia,{platform:"new-platform",url:"https://",icon:"ri-link-line"}]})},className:"btn-success",children:[(0,a.jsx)("i",{className:"ri-add-line ml-2"}),"إضافة موقع"]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:c.socialMedia.map((e,t)=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:i?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"المنصة"}),(0,a.jsx)("button",{onClick:()=>(e=>{let t=c.socialMedia.filter((t,s)=>s!==e);d({...c,socialMedia:t})})(t),className:"text-red-600 hover:text-red-800",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]}),(0,a.jsx)("input",{type:"text",value:e.platform,onChange:e=>x(t,"platform",e.target.value),className:"form-input",placeholder:"اسم المنصة"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الأيقونة"}),(0,a.jsx)("select",{value:e.icon,onChange:e=>x(t,"icon",e.target.value),className:"form-input",children:b.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الرابط"}),(0,a.jsx)("input",{type:"url",value:e.url,onChange:e=>x(t,"url",e.target.value),className:"form-input",placeholder:"https://..."})]})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"".concat(e.icon," text-blue-600")})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:e.platform}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.url})]})]})},t))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-gray-900 flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-links-line text-blue-600"}),(0,a.jsx)("span",{children:"الروابط السريعة"})]}),i&&(0,a.jsxs)("button",{onClick:()=>{d({...c,quickLinks:[...c.quickLinks,{href:"#",text:"رابط جديد"}]})},className:"btn-success",children:[(0,a.jsx)("i",{className:"ri-add-line ml-2"}),"إضافة رابط"]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:c.quickLinks.map((e,t)=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:i?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"الرابط"}),(0,a.jsx)("button",{onClick:()=>(e=>{let t=c.quickLinks.filter((t,s)=>s!==e);d({...c,quickLinks:t})})(t),className:"text-red-600 hover:text-red-800",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]}),(0,a.jsx)("input",{type:"text",value:e.text,onChange:e=>u(t,"text",e.target.value),className:"form-input",placeholder:"نص الرابط"}),(0,a.jsx)("input",{type:"text",value:e.href,onChange:e=>u(t,"href",e.target.value),className:"form-input",placeholder:"عنوان الرابط"})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:e.text}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.href})]})},t))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-gray-900 flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-contacts-line text-blue-600"}),(0,a.jsx)("span",{children:"معلومات التواصل"})]}),i&&(0,a.jsxs)("button",{onClick:()=>{d({...c,contactInfo:[...c.contactInfo,{icon:"ri-information-line",text:"معلومة جديدة"}]})},className:"btn-success",children:[(0,a.jsx)("i",{className:"ri-add-line ml-2"}),"إضافة معلومة"]})]}),(0,a.jsx)("div",{className:"space-y-4",children:c.contactInfo.map((e,t)=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:i?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"معلومة التواصل"}),(0,a.jsx)("button",{onClick:()=>(e=>{let t=c.contactInfo.filter((t,s)=>s!==e);d({...c,contactInfo:t})})(t),className:"text-red-600 hover:text-red-800",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الأيقونة"}),(0,a.jsx)("select",{value:e.icon,onChange:e=>h(t,"icon",e.target.value),className:"form-input",children:p.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"النص"}),(0,a.jsx)("textarea",{value:e.text,onChange:e=>h(t,"text",e.target.value),className:"form-textarea",rows:2,placeholder:"معلومات التواصل"})]})]}):(0,a.jsxs)("div",{className:"flex items-start space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mt-1",children:(0,a.jsx)("i",{className:"".concat(e.icon," text-green-600")})}),(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-gray-900",children:e.text})})]})},t))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-copyright-line text-blue-600"}),(0,a.jsx)("span",{children:"حقوق الطبع والنشر"})]}),i?(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"نص حقوق الطبع والنشر"}),(0,a.jsx)("input",{type:"text",value:c.copyright,onChange:e=>{var t;return t=e.target.value,void d({...c,copyright:t})},className:"form-input",placeholder:"\xa9 2024 عجائب الخبراء. جميع الحقوق محفوظة."})]}):(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-gray-900 text-center",children:c.copyright})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-eye-line text-blue-600"}),(0,a.jsx)("span",{children:"معاينة الفوتر"})]}),(0,a.jsxs)("div",{className:"bg-gray-900 text-white p-8 rounded-lg",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-4",children:"تابعنا"}),(0,a.jsx)("div",{className:"flex space-x-4 rtl:space-x-reverse",children:c.socialMedia.map((e,t)=>(0,a.jsx)("a",{href:e.url,className:"w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors",children:(0,a.jsx)("i",{className:e.icon})},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-4",children:"روابط سريعة"}),(0,a.jsx)("ul",{className:"space-y-2",children:c.quickLinks.map((e,t)=>(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:e.href,className:"text-gray-300 hover:text-white transition-colors",children:e.text})},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-4",children:"تواصل معنا"}),(0,a.jsx)("div",{className:"space-y-3",children:c.contactInfo.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"".concat(e.icon," text-blue-400 mt-1")}),(0,a.jsx)("span",{className:"text-gray-300 text-sm",children:e.text})]},t))})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-700 mt-8 pt-8 text-center",children:(0,a.jsx)("p",{className:"text-gray-400",children:c.copyright})})]})]})]})},L=()=>{var e,t;let{user:s,updateUser:r}=p(),[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)({username:(null==s?void 0:s.username)||"",email:(null==s?void 0:s.email)||"",currentPassword:"",newPassword:"",confirmPassword:""}),[o,m]=(0,l.useState)(!1),[x,u]=(0,l.useState)({current:!1,new:!1,confirm:!1}),h=e=>{d({...c,[e.target.name]:e.target.value})},g=e=>{u({...x,[e]:!x[e]})},b=async()=>{if(!c.username.trim()||!c.email.trim())return void alert("يرجى ملء جميع الحقول المطلوبة");if(c.newPassword&&c.newPassword!==c.confirmPassword)return void alert("كلمة المرور الجديدة وتأكيدها غير متطابقين");if(c.newPassword&&c.newPassword.length<6)return void alert("كلمة المرور يجب أن تكون 6 أحرف على الأقل");m(!0);try{await new Promise(e=>setTimeout(e,1e3));let e={username:c.username,email:c.email};c.newPassword&&(e.password=c.newPassword),r(e),n(!1),d({...c,currentPassword:"",newPassword:"",confirmPassword:""}),alert("تم تحديث البيانات بنجاح!")}catch(e){alert("حدث خطأ أثناء التحديث")}finally{m(!1)}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"إدارة المستخدمين"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"تعديل بيانات المستخدم وكلمة المرور"})]}),(0,a.jsx)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{d({username:(null==s?void 0:s.username)||"",email:(null==s?void 0:s.email)||"",currentPassword:"",newPassword:"",confirmPassword:""}),n(!1)},className:"btn-secondary",disabled:o,children:"إلغاء"}),(0,a.jsx)("button",{onClick:b,className:"btn-primary",disabled:o,children:o?"جاري الحفظ...":"حفظ التغييرات"})]}):(0,a.jsxs)("button",{onClick:()=>n(!0),className:"btn-primary",children:[(0,a.jsx)("i",{className:"ri-edit-line ml-2"}),"تعديل البيانات"]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-6 rtl:space-x-reverse mb-8",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-3xl",children:(null==s||null==(t=s.username)||null==(e=t.charAt(0))?void 0:e.toUpperCase())||"A"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:(null==s?void 0:s.username)||"المدير"}),(0,a.jsx)("p",{className:"text-gray-600",children:(null==s?void 0:s.email)||"<EMAIL>"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse mt-2",children:[(0,a.jsx)("span",{className:"badge badge-success",children:"مدير النظام"}),(0,a.jsx)("span",{className:"badge badge-info",children:"نشط"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"المعلومات الأساسية"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم المستخدم *"}),i?(0,a.jsx)("input",{type:"text",name:"username",value:c.username,onChange:h,className:"form-input",placeholder:"أدخل اسم المستخدم"}):(0,a.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg",children:(0,a.jsx)("span",{className:"text-gray-900",children:(null==s?void 0:s.username)||"غير محدد"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني *"}),i?(0,a.jsx)("input",{type:"email",name:"email",value:c.email,onChange:h,className:"form-input",placeholder:"أدخل البريد الإلكتروني"}):(0,a.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg",children:(0,a.jsx)("span",{className:"text-gray-900",children:(null==s?void 0:s.email)||"غير محدد"})})]})]})]}),i&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"تغيير كلمة المرور"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"كلمة المرور الحالية"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:x.current?"text":"password",name:"currentPassword",value:c.currentPassword,onChange:h,className:"form-input pr-12",placeholder:"أدخل كلمة المرور الحالية"}),(0,a.jsx)("button",{type:"button",onClick:()=>g("current"),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,a.jsx)("i",{className:x.current?"ri-eye-off-line":"ri-eye-line"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"كلمة المرور الجديدة"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:x.new?"text":"password",name:"newPassword",value:c.newPassword,onChange:h,className:"form-input pr-12",placeholder:"أدخل كلمة المرور الجديدة"}),(0,a.jsx)("button",{type:"button",onClick:()=>g("new"),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,a.jsx)("i",{className:x.new?"ri-eye-off-line":"ri-eye-line"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"تأكيد كلمة المرور الجديدة"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:x.confirm?"text":"password",name:"confirmPassword",value:c.confirmPassword,onChange:h,className:"form-input pr-12",placeholder:"أعد إدخال كلمة المرور الجديدة"}),(0,a.jsx)("button",{type:"button",onClick:()=>g("confirm"),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,a.jsx)("i",{className:x.confirm?"ri-eye-off-line":"ri-eye-line"})})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-800 mb-2",children:"متطلبات كلمة المرور:"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsxs)("li",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-check-line ".concat(c.newPassword.length>=6?"text-green-600":"text-gray-400")}),(0,a.jsx)("span",{children:"6 أحرف على الأقل"})]}),(0,a.jsxs)("li",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-check-line ".concat(c.newPassword===c.confirmPassword&&c.newPassword?"text-green-600":"text-gray-400")}),(0,a.jsx)("span",{children:"تطابق كلمة المرور وتأكيدها"})]})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-6",children:"إحصائيات الحساب"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-blue-50 rounded-xl",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"ri-login-box-line text-2xl text-white"})}),(0,a.jsx)("h4",{className:"text-2xl font-bold text-blue-600",children:"1"}),(0,a.jsx)("p",{className:"text-blue-800 font-medium",children:"جلسة نشطة"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-green-50 rounded-xl",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"ri-time-line text-2xl text-white"})}),(0,a.jsx)("h4",{className:"text-2xl font-bold text-green-600",children:"اليوم"}),(0,a.jsx)("p",{className:"text-green-800 font-medium",children:"آخر تسجيل دخول"})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-purple-50 rounded-xl",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"ri-shield-check-line text-2xl text-white"})}),(0,a.jsx)("h4",{className:"text-2xl font-bold text-purple-600",children:"آمن"}),(0,a.jsx)("p",{className:"text-purple-800 font-medium",children:"حالة الأمان"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-6",children:"إعدادات الأمان"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-shield-check-line text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"المصادقة الثنائية"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"حماية إضافية لحسابك"})]})]}),(0,a.jsx)("span",{className:"badge badge-success",children:"مفعل"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-notification-line text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"تنبيهات الأمان"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"إشعارات عند تسجيل الدخول"})]})]}),(0,a.jsx)("span",{className:"badge badge-success",children:"مفعل"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-history-line text-orange-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"سجل النشاط"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"تتبع جميع العمليات"})]})]}),(0,a.jsx)("span",{className:"badge badge-success",children:"مفعل"})]})]})]})]})};var A=s(5506);let M=e=>{let{children:t,className:s="",onClick:r,isActive:i=!1,delay:n=0,hoverEffect:c=!0,rippleEffect:d=!0}=e,[o,m]=(0,l.useState)([]);return(0,a.jsxs)("div",{className:"\n        admin-card relative overflow-hidden cursor-pointer\n        transition-all duration-300 ease-out\n        ".concat(c?"hover:shadow-xl hover:-translate-y-2":"","\n        ").concat(i?"ring-2 ring-blue-500 ring-opacity-50":"","\n        ").concat(s,"\n      "),onClick:e=>{if(d){let t=e.currentTarget.getBoundingClientRect(),s=Math.max(t.width,t.height),a={x:e.clientX-t.left-s/2,y:e.clientY-t.top-s/2,size:s,id:Date.now()};m(e=>[...e,a]),setTimeout(()=>{m(e=>e.filter(e=>e.id!==a.id))},600)}r&&r(e)},style:{animationDelay:"".concat(n,"ms"),animationFillMode:"both"},children:[t,o.map(e=>(0,a.jsx)("span",{className:"absolute bg-white/30 rounded-full pointer-events-none animate-ping",style:{left:e.x,top:e.y,width:e.size,height:e.size,animationDuration:"600ms"}},e.id)),c&&(0,a.jsx)("div",{className:"absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-700"})})]})},B=e=>{let{title:t,value:s,icon:r,color:i="blue",trend:n=null,delay:c=0,prefix:d="",suffix:o=""}=e,[m,x]=(0,l.useState)(0);(0,l.useEffect)(()=>{let e=setTimeout(()=>{let e=s/60,t=0,a=setInterval(()=>{(t+=e)>=s?(x(s),clearInterval(a)):x(Math.floor(t))},1e3/60);return()=>clearInterval(a)},c);return()=>clearTimeout(e)},[s,c]);let u={blue:{bg:"from-blue-500 to-blue-600",text:"text-blue-600",bgLight:"bg-blue-50",ring:"ring-blue-500/20"},green:{bg:"from-emerald-500 to-emerald-600",text:"text-emerald-600",bgLight:"bg-emerald-50",ring:"ring-emerald-500/20"},purple:{bg:"from-purple-500 to-purple-600",text:"text-purple-600",bgLight:"bg-purple-50",ring:"ring-purple-500/20"},orange:{bg:"from-orange-500 to-orange-600",text:"text-orange-600",bgLight:"bg-orange-50",ring:"ring-orange-500/20"},red:{bg:"from-red-500 to-red-600",text:"text-red-600",bgLight:"bg-red-50",ring:"ring-red-500/20"}},h=u[i]||u.blue;return(0,a.jsxs)(M,{className:"p-6 bg-white rounded-2xl border border-gray-200/50 shadow-sm hover:shadow-lg ".concat(h.ring," hover:ring-4"),delay:c,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:t}),(0,a.jsxs)("div",{className:"flex items-baseline space-x-2 rtl:space-x-reverse",children:[(0,a.jsxs)("span",{className:"text-3xl font-bold text-gray-900",children:[d,m.toLocaleString("ar-SA"),o]}),n&&(0,a.jsxs)("span",{className:"\n                inline-flex items-center px-2 py-1 rounded-full text-xs font-medium\n                ".concat("up"===n.type?"bg-green-100 text-green-800":"down"===n.type?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800","\n              "),children:["up"===n.type&&(0,a.jsx)("i",{className:"ri-arrow-up-line mr-1"}),"down"===n.type&&(0,a.jsx)("i",{className:"ri-arrow-down-line mr-1"}),n.value]})]})]}),(0,a.jsx)("div",{className:"\n          w-16 h-16 rounded-2xl bg-gradient-to-br ".concat(h.bg," \n          flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-110\n        "),children:(0,a.jsx)("i",{className:"".concat(r," text-2xl text-white")})})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("div",{className:"w-full h-2 ".concat(h.bgLight," rounded-full overflow-hidden"),children:(0,a.jsx)("div",{className:"h-full bg-gradient-to-r ".concat(h.bg," rounded-full transition-all duration-1000 ease-out"),style:{width:"".concat(Math.min(m/s*100,100),"%"),transitionDelay:"".concat(c+500,"ms")}})})})]})},U=()=>{var e;let{kitchensData:t,cabinetsData:s,whyChooseUsData:l,refreshData:r}=w(),i=async()=>{if(window.confirm("هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات الحالية."))try{await (0,A.aB)(),await r(),alert("تم إعادة تعيين قاعدة البيانات بنجاح!"),window.location.reload()}catch(e){alert("حدث خطأ أثناء إعادة تعيين قاعدة البيانات")}},n=[{title:"إجمالي المطابخ",value:(null==t?void 0:t.length)||0,icon:"ri-home-4-line",color:"blue",trend:{type:"up",value:"+12%"}},{title:"إجمالي الخزائن",value:(null==s?void 0:s.length)||0,icon:"ri-archive-drawer-line",color:"purple",trend:{type:"up",value:"+8%"}},{title:"المميزات",value:(null==l||null==(e=l.features)?void 0:e.length)||0,icon:"ri-star-smile-line",color:"green",trend:{type:"stable",value:"0%"}},{title:"إجمالي الصور",value:((null==t?void 0:t.reduce((e,t)=>{var s;return e+((null==(s=t.images)?void 0:s.length)||0)},0))||0)+((null==s?void 0:s.reduce((e,t)=>{var s;return e+((null==(s=t.images)?void 0:s.length)||0)},0))||0),icon:"ri-image-2-line",color:"orange",trend:{type:"up",value:"+25%"}}];return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)(M,{className:"bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-3xl p-8 text-white shadow-2xl",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse mb-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm",children:(0,a.jsx)("i",{className:"ri-dashboard-3-line text-2xl text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl font-bold",children:"مرحباً بك في لوحة التحكم"}),(0,a.jsx)("p",{className:"text-blue-100 text-sm md:text-lg",children:"إدارة محتوى موقع عجائب الخبراء"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-green-100",children:"النظام يعمل بشكل طبيعي"})]}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("i",{className:"ri-time-line text-blue-200"}),(0,a.jsx)("span",{className:"text-blue-200",children:new Date().toLocaleString("ar-SA")})]})]})]}),(0,a.jsx)("div",{className:"hidden lg:block",children:(0,a.jsx)("div",{className:"w-24 h-24 bg-white/10 rounded-3xl flex items-center justify-center backdrop-blur-sm border border-white/20",children:(0,a.jsx)("i",{className:"ri-admin-line text-4xl text-white"})})})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:n.map((e,t)=>(0,a.jsx)(B,{title:e.title,value:e.value,icon:e.icon,color:e.color,trend:e.trend,delay:100*t},t))}),(0,a.jsxs)(M,{className:"bg-white rounded-3xl p-6 shadow-sm border border-gray-200/50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse mb-6",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-flashlight-line text-white text-lg"})}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"إجراءات سريعة"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[{title:"إضافة مطبخ جديد",description:"أضف تصميم مطبخ جديد إلى المعرض",icon:"ri-add-line",color:"from-blue-500 to-blue-600",action:"kitchens"},{title:"إضافة خزانة جديدة",description:"أضف تصميم خزانة جديد إلى المعرض",icon:"ri-add-line",color:"from-purple-500 to-purple-600",action:"cabinets"},{title:"تحديث الهيرو",description:"عدّل النصوص والصور في القسم الرئيسي",icon:"ri-edit-line",color:"from-green-500 to-green-600",action:"hero"},{title:"إدارة المميزات",description:"أضف أو عدّل مميزات الشركة",icon:"ri-settings-line",color:"from-orange-500 to-orange-600",action:"why-choose-us"}].map((e,t)=>(0,a.jsxs)(M,{className:"p-6 border border-gray-200/50 rounded-2xl hover:border-blue-300 hover:shadow-lg text-right group bg-white",delay:100*t,children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br ".concat(e.color," rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg"),children:(0,a.jsx)("i",{className:"".concat(e.icon," text-xl text-white")})}),(0,a.jsx)("h3",{className:"font-bold text-gray-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.description}),(0,a.jsxs)("div",{className:"mt-4 flex items-center text-blue-600 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsx)("span",{children:"انقر للبدء"}),(0,a.jsx)("i",{className:"ri-arrow-left-s-line mr-1"})]})]},t))})]}),(0,a.jsxs)(M,{className:"bg-white rounded-3xl p-6 shadow-sm border border-gray-200/50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse mb-6",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-history-line text-white text-lg"})}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"النشاط الأخير"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-2xl border border-blue-200/50",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg",children:(0,a.jsx)("i",{className:"ri-login-box-line text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-bold text-gray-900",children:"تم تسجيل الدخول بنجاح"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 font-medium",children:new Date().toLocaleString("ar-SA")})]}),(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gradient-to-r from-emerald-50 to-emerald-100/50 rounded-2xl border border-emerald-200/50",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg",children:(0,a.jsx)("i",{className:"ri-check-line text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-bold text-gray-900",children:"تم تحميل البيانات بنجاح"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 font-medium",children:"منذ دقائق قليلة"})]}),(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gradient-to-r from-purple-50 to-purple-100/50 rounded-2xl border border-purple-200/50",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg",children:(0,a.jsx)("i",{className:"ri-database-2-line text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-bold text-gray-900",children:"تم تحديث قاعدة البيانات"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 font-medium",children:"منذ ساعة"})]}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(M,{className:"bg-white rounded-3xl shadow-sm border border-gray-200/50 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse mb-6",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-settings-3-line text-white text-lg"})}),(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"معلومات النظام"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-xl",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"إصدار النظام:"}),(0,a.jsx)("span",{className:"font-bold text-gray-900 bg-blue-100 text-blue-800 px-3 py-1 rounded-lg text-sm",children:"v2.0.0"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-xl",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"آخر تحديث:"}),(0,a.jsx)("span",{className:"font-bold text-gray-900",children:"اليوم"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-xl",children:[(0,a.jsx)("span",{className:"text-gray-600 font-medium",children:"حالة النظام:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-green-600 font-bold",children:"متصل"})]})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:(0,a.jsxs)("button",{onClick:i,className:"w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white py-3 px-4 rounded-2xl transition-all duration-300 text-sm font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:[(0,a.jsx)("i",{className:"ri-refresh-line mr-2"}),"إعادة تعيين قاعدة البيانات"]})})]}),(0,a.jsxs)(M,{className:"bg-white rounded-3xl shadow-sm border border-gray-200/50 p-6",delay:200,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse mb-6",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-lightbulb-line text-white text-lg"})}),(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"نصائح سريعة"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3 rtl:space-x-reverse p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl border border-yellow-200/50",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,a.jsx)("i",{className:"ri-image-line text-white text-sm"})}),(0,a.jsx)("span",{className:"text-sm text-gray-700 font-medium leading-relaxed",children:"استخدم صور عالية الجودة للحصول على أفضل النتائج"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3 rtl:space-x-reverse p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200/50",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,a.jsx)("i",{className:"ri-edit-line text-white text-sm"})}),(0,a.jsx)("span",{className:"text-sm text-gray-700 font-medium leading-relaxed",children:"احرص على كتابة أوصاف واضحة ومفيدة"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3 rtl:space-x-reverse p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl border border-emerald-200/50",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-xl flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,a.jsx)("i",{className:"ri-save-line text-white text-sm"})}),(0,a.jsx)("span",{className:"text-sm text-gray-700 font-medium leading-relaxed",children:"قم بحفظ التغييرات بانتظام"})]})]})]})]})]})},D=()=>{var e,t;let[s,r]=(0,l.useState)("dashboard"),[i,n]=(0,l.useState)(!1),{user:c,logout:d}=p();(0,l.useEffect)(()=>{let e=setTimeout(()=>{n(!0)},100);return()=>clearTimeout(e)},[]),(0,l.useEffect)(()=>{n(!1);let e=setTimeout(()=>{n(!0)},50);return()=>clearTimeout(e)},[s]);let o=[{id:"dashboard",name:"الرئيسية",icon:"ri-dashboard-3-line",color:"from-blue-500 to-blue-600",bgColor:"bg-blue-50",description:"نظرة عامة"},{id:"hero",name:"الهيرو",icon:"ri-image-2-line",color:"from-purple-500 to-purple-600",bgColor:"bg-purple-50",description:"القسم الرئيسي"},{id:"why-choose-us",name:"مميزاتنا",icon:"ri-star-smile-line",color:"from-emerald-500 to-emerald-600",bgColor:"bg-emerald-50",description:"لماذا تختارنا"},{id:"kitchens",name:"المطابخ",icon:"ri-home-4-line",color:"from-orange-500 to-orange-600",bgColor:"bg-orange-50",description:"معرض المطابخ"},{id:"cabinets",name:"الخزائن",icon:"ri-archive-drawer-line",color:"from-teal-500 to-teal-600",bgColor:"bg-teal-50",description:"معرض الخزائن"},{id:"footer",name:"الفوتر",icon:"ri-links-line",color:"from-indigo-500 to-indigo-600",bgColor:"bg-indigo-50",description:"معلومات التواصل"},{id:"users",name:"المستخدمين",icon:"ri-user-settings-line",color:"from-pink-500 to-pink-600",bgColor:"bg-pink-50",description:"إدارة الحسابات"}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/50 to-indigo-50/50 relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-30",children:(0,a.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),\n                           radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%)"}})}),(0,a.jsx)("header",{className:"sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-sm",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,a.jsx)("i",{className:"ri-admin-line text-white text-lg"})}),(0,a.jsxs)("div",{className:"hidden sm:block",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"لوحة التحكم"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"عجائب الخبراء"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,a.jsxs)("button",{className:"relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"ri-notification-line text-xl"}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:"3"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:(null==c||null==(t=c.username)||null==(e=t.charAt(0))?void 0:e.toUpperCase())||"A"})}),(0,a.jsxs)("div",{className:"hidden md:block text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:(null==c?void 0:c.username)||"المدير"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"مدير النظام"})]}),(0,a.jsx)("button",{onClick:()=>{window.confirm("هل أنت متأكد من تسجيل الخروج؟")&&d()},className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"تسجيل الخروج",children:(0,a.jsx)("i",{className:"ri-logout-box-line text-lg"})})]})]})]})})}),(0,a.jsxs)("main",{className:"relative z-10",children:[(0,a.jsx)("div",{className:"hidden md:block p-6",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,a.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:o.map((e,t)=>(0,a.jsxs)("button",{onClick:()=>r(e.id),className:"\n                    group relative p-6 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1\n                    ".concat(s===e.id?"bg-gradient-to-br ".concat(e.color," text-white shadow-xl shadow-").concat(e.color.split("-")[1],"-500/25"):"".concat(e.bgColor," hover:bg-white text-gray-700 hover:shadow-lg border border-gray-200/50"),"\n                  "),style:{animationDelay:"".concat(100*t,"ms")},children:[(0,a.jsxs)("div",{className:"flex flex-col items-center text-center space-y-3",children:[(0,a.jsx)("div",{className:"\n                      w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300\n                      ".concat(s===e.id?"bg-white/20":"bg-gradient-to-br ".concat(e.color," text-white group-hover:scale-110"),"\n                    "),children:(0,a.jsx)("i",{className:"".concat(e.icon," text-xl")})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-lg mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-sm ".concat(s===e.id?"text-white/80":"text-gray-500"),children:e.description})]})]}),s===e.id&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-2xl"})]},e.id))})})}),(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 pb-20 md:pb-8",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,a.jsx)("div",{className:"transition-all duration-500 ".concat(i?"opacity-100 translate-y-0":"opacity-0 translate-y-4"),children:(()=>{switch(s){case"dashboard":default:return(0,a.jsx)(U,{});case"hero":return(0,a.jsx)(C,{});case"why-choose-us":return(0,a.jsx)(_,{});case"kitchens":return(0,a.jsx)(I,{});case"cabinets":return(0,a.jsx)(P,{});case"footer":return(0,a.jsx)(T,{});case"users":return(0,a.jsx)(L,{})}})()})})})]}),(0,a.jsx)(v,{activeSection:s,setActiveSection:r,menuItems:o})]})};function O(){let{isAuthenticated:e,loading:t}=p(),[s,r]=(0,l.useState)(!1),[n,c]=(0,l.useState)(!0);return((0,l.useEffect)(()=>{(async()=>{try{c(!0),await (0,A.wU)()?r(!0):r(!1)}catch(e){r(!1)}finally{c(!1)}})()},[]),t||n)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"}),(0,a.jsx)("p",{className:"text-gray-600 text-xl font-bold",children:n?"جاري تهيئة قاعدة البيانات...":"جاري التحميل..."})]})}):s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(i(),{children:[(0,a.jsx)("title",{children:"لوحة الإدارة - عجائب الخبراء"}),(0,a.jsx)("meta",{name:"description",content:"لوحة إدارة موقع عجائب الخبراء - منطقة محظورة للمديرين فقط"}),(0,a.jsx)("meta",{name:"robots",content:"noindex, nofollow, noarchive, nosnippet"}),(0,a.jsx)("meta",{name:"googlebot",content:"noindex, nofollow"}),(0,a.jsx)("meta",{name:"bingbot",content:"noindex, nofollow"})]}),(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30",children:e?(0,a.jsx)(k,{children:(0,a.jsx)(D,{})}):(0,a.jsx)(f,{})})]}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-2xl text-red-500"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-red-600 mb-4",children:"خطأ في تهيئة قاعدة البيانات"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"حدث خطأ أثناء تهيئة قاعدة البيانات. يرجى إعادة تحميل الصفحة أو الاتصال بالدعم التقني."}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-bold transition-colors",children:"إعادة تحميل الصفحة"})]})})}let X=function(){return(0,a.jsx)(j,{children:(0,a.jsx)(O,{})})}},7328:(e,t,s)=>{e.exports=s(9836)},8210:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin",function(){return s(6221)}])}},e=>{e.O(0,[636,593,792],()=>e(e.s=8210)),_N_E=e.O()}]);